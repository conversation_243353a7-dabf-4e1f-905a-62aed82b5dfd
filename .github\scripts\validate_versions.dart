// Version validation script
// This script validates that app versions and database schema versions are consistent

import 'dart:io';

void main() {
  print('Starting version validation...');
  
  bool success = true;
  
  // Check if version files exist
  final versionInfoFile = File('lib/core/version/version_info.dart');
  final dbVersionManagerFile = File('lib/core/version/database_version_manager.dart');
  final pubspecFile = File('pubspec.yaml');
  
  if (!versionInfoFile.existsSync()) {
    print('❌ Error: version_info.dart file not found');
    success = false;
  }
  
  if (!dbVersionManagerFile.existsSync()) {
    print('❌ Error: database_version_manager.dart file not found');
    success = false;
  }
  
  if (!pubspecFile.existsSync()) {
    print('❌ Error: pubspec.yaml file not found');
    success = false;
  }
  
  if (!success) {
    exit(1);
  }
  
  // Read file contents
  final versionInfoContent = versionInfoFile.readAsStringSync();
  final dbVersionManagerContent = dbVersionManagerFile.readAsStringSync();
  final pubspecContent = pubspecFile.readAsStringSync();
  
  // Extract version from version_info.dart
  final versionMatch = RegExp(r'static const String version = [\'"]([^\'"]+)[\'"]').firstMatch(versionInfoContent);
  final buildNumberMatch = RegExp(r'static const int buildNumber = (\d+)').firstMatch(versionInfoContent);
  
  if (versionMatch == null || buildNumberMatch == null) {
    print('❌ Error: Could not extract version information from version_info.dart');
    success = false;
  }
  
  // Extract version from pubspec.yaml
  final pubspecVersionMatch = RegExp(r'version: ([^\s]+)').firstMatch(pubspecContent);
  
  if (pubspecVersionMatch == null) {
    print('❌ Error: Could not extract version information from pubspec.yaml');
    success = false;
  }
  
  if (!success) {
    exit(1);
  }
  
  final appVersion = versionMatch!.group(1)!;
  final buildNumber = int.parse(buildNumberMatch!.group(1)!);
  final pubspecVersion = pubspecVersionMatch!.group(1)!;
  final expectedPubspecVersion = '$appVersion+$buildNumber';
  
  // Validate that versions match
  if (pubspecVersion != expectedPubspecVersion) {
    print('❌ Error: Version mismatch between version_info.dart and pubspec.yaml');
    print('   version_info.dart: $expectedPubspecVersion');
    print('   pubspec.yaml: $pubspecVersion');
    success = false;
  } else {
    print('✅ App version in version_info.dart matches pubspec.yaml: $pubspecVersion');
  }
  
  // Extract version map from database_version_manager.dart
  final versionMapRegex = RegExp(r'static const Map<String, int> _versionMap = \{([^}]+)\}', dotAll: true);
  final versionMapMatch = versionMapRegex.firstMatch(dbVersionManagerContent);
  
  if (versionMapMatch == null) {
    print('❌ Error: Could not extract version map from database_version_manager.dart');
    success = false;
    exit(1);
  }
  
  final versionMapContent = versionMapMatch.group(1)!;
  final versionEntries = RegExp(r"'([^']+)':\s*(\d+)").allMatches(versionMapContent);
  
  // Check if current app version is in the database version map
  bool appVersionFound = false;
  for (final entry in versionEntries) {
    final mapVersion = entry.group(1)!;
    if (mapVersion == appVersion) {
      appVersionFound = true;
      print('✅ App version $appVersion found in database version map with schema version ${entry.group(2)}');
      break;
    }
  }
  
  if (!appVersionFound) {
    print('❌ Error: Current app version $appVersion not found in database version map');
    print('   Make sure to add the current app version to the _versionMap in database_version_manager.dart');
    success = false;
  }
  
  // Check for duplicate schema versions
  final schemaVersions = <String, int>{};
  final duplicateSchemas = <String>{};
  
  for (final entry in versionEntries) {
    final mapVersion = entry.group(1)!;
    final schemaVersion = int.parse(entry.group(2)!);
    
    if (schemaVersions.containsValue(schemaVersion)) {
      final existingVersion = schemaVersions.entries
          .firstWhere((element) => element.value == schemaVersion)
          .key;
      
      print('⚠️ Warning: Duplicate schema version $schemaVersion for app versions $existingVersion and $mapVersion');
      duplicateSchemas.add('$existingVersion, $mapVersion (schema: $schemaVersion)');
    }
    
    schemaVersions[mapVersion] = schemaVersion;
  }
  
  if (duplicateSchemas.isNotEmpty) {
    print('⚠️ Warning: Found duplicate schema versions:');
    for (final duplicate in duplicateSchemas) {
      print('   $duplicate');
    }
    print('   This is not necessarily an error, but make sure it\'s intentional.');
  }
  
  // Final result
  if (success) {
    print('✅ Version validation completed successfully!');
    exit(0);
  } else {
    print('❌ Version validation failed!');
    exit(1);
  }
}
