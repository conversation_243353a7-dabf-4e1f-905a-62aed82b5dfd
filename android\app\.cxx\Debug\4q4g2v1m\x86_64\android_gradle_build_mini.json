{"buildFiles": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "S:\\DEV\\englr\\kiq\\android\\app\\.cxx\\Debug\\4q4g2v1m\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "S:\\DEV\\englr\\kiq\\android\\app\\.cxx\\Debug\\4q4g2v1m\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}