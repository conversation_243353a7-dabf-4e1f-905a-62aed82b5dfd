{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/home/<USER>/Android/Sdk/cmake/3.22.1/bin/cmake", "cpack": "/home/<USER>/Android/Sdk/cmake/3.22.1/bin/cpack", "ctest": "/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ctest", "root": "/home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": false, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-0d6417688be8ba62a2b5.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-4032f4e74cb88f4b45c7.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-85e6d5b6ac9f77b77fff.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-4032f4e74cb88f4b45c7.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-85e6d5b6ac9f77b77fff.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-0d6417688be8ba62a2b5.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}