import 'package:drift/drift.dart';
import '../datasources/app_database.dart';
import 'database_repository.dart';

/// Repository for Performance table operations
class PerformanceRepository extends BaseDatabaseRepository<PerformanceData, PerformanceCompanion> {
  PerformanceRepository(AppDatabase db) : super(db, db.performance);

  @override
  Future<List<PerformanceData>> getAll() {
    return (db.select(db.performance)
      ..where((t) => t.deletedAt.isNull())
      ..orderBy([(t) => OrderingTerm.desc(t.date)])
    ).get();
  }

  @override
  Future<PerformanceData?> getById(int id) {
    return (db.select(db.performance)
      ..where((t) => t.id.equals(id))
      ..where((t) => t.deletedAt.isNull())
    ).getSingleOrNull();
  }

  @override
  Future<bool> update(PerformanceData entity) {
    return db.update(db.performance).replace(entity);
  }

  @override
  Future<int> delete(int id) {
    return (db.delete(db.performance)..where((t) => t.id.equals(id))).go();
  }

  @override
  Future<List<PerformanceData>> getUnsyncedRecords() {
    return (db.select(db.performance)
      ..where((t) => t.syncStatus.equals('pendingUpload'))
    ).get();
  }

  @override
  Future<bool> updateWithSync(PerformanceData entity) async {
    // First update the entity
    final bool updated = await update(entity);

    // Then separately update the sync status fields if the entity update succeeded
    if (updated) {
      await db.customUpdate(
        'UPDATE performance SET updated_at = ?, sync_status = ? WHERE id = ?',
        variables: [
          Variable(DateTime.now().toUtc().toIso8601String()),
          const Variable('pendingUpload'),
          Variable(entity.id),
        ],
        updateKind: UpdateKind.update,
      );
    }

    return updated;
  }

  @override
  Future<bool> softDelete(int id) async {
    return (db.update(db.performance)..where((t) => t.id.equals(id))).write(
      PerformanceCompanion(
        deletedAt: Value(DateTime.now().toUtc()),
        syncStatus: const Value(SyncStatus.pendingUpload),
      ),
    ).then((rows) => rows > 0);
  }

  @override
  Future<void> markAsSynced(String uuid) async {
    await (db.update(db.performance)..where((t) => t.uuid.equals(uuid))).write(
      const PerformanceCompanion(
        syncStatus: Value(SyncStatus.synced),
      ),
    );
  }



  /// Get performance records for a specific date range
  Future<List<PerformanceData>> getPerformanceForDateRange(DateTime start, DateTime end) {
    return (db.select(db.performance)
      ..where((t) => t.deletedAt.isNull())
      ..where((t) => t.date.isBiggerOrEqualValue(start))
      ..where((t) => t.date.isSmallerOrEqualValue(end))
      ..orderBy([(t) => OrderingTerm.desc(t.date)])
    ).get();
  }

  /// Check if a performance record already exists for a specific date
  Future<bool> checkDateExists(DateTime date, {int? excludeId}) async {
    final query = db.select(db.performance)
      ..where((t) => t.deletedAt.isNull())
      ..where((t) => t.date.equals(date));

    if (excludeId != null) {
      query.where((t) => t.id.isNotValue(excludeId));
    }

    final count = await query.get().then((records) => records.length);
    return count > 0;
  }
}
