#!/bin/sh
#
# commit-msg hook to validate commit message format
#
# To enable this hook, copy it to .git/hooks/commit-msg

# Get the commit message (first argument is the file containing the message)
commit_msg_file="$1"
commit_msg=$(cat "$commit_msg_file")

# Skip merge commits
if echo "$commit_msg" | grep -q "^Merge"; then
  exit 0
fi

# Check for conventional commit format
if ! echo "$commit_msg" | grep -qE '^(feat|fix|docs|style|refactor|perf|test|build|ci|chore)(\([a-z0-9-]+\))?!?: .+'; then
  echo "Error: Commit message does not follow conventional commits format."
  echo "Required format: <type>(<scope>): <description>"
  echo "Examples:"
  echo "  feat(auth): add biometric authentication"
  echo "  fix(sync): resolve data synchronization issue"
  echo "  docs: update README with new instructions"
  echo ""
  echo "Types: feat, fix, docs, style, refactor, perf, test, build, ci, chore"
  echo "Add ! before : for breaking changes, e.g., feat!: breaking change"
  exit 1
fi

# Validate description length (should be at least 10 characters)
description=$(echo "$commit_msg" | sed -E 's/^(feat|fix|docs|style|refactor|perf|test|build|ci|chore)(\([a-z0-9-]+\))?!?: (.+)/\3/')
if [ ${#description} -lt 10 ]; then
  echo "Error: Commit message description is too short (less than 10 characters)."
  echo "Please provide a more descriptive message."
  exit 1
fi

exit 0
