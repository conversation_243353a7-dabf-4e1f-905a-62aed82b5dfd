import '../datasources/app_database.dart';

/// Repository for LevelSettings table operations
class LevelSettingsRepository {
  final AppDatabase db;

  LevelSettingsRepository(this.db);

  /// Get the level settings
  Future<LevelSetting?> getLevelSettings() {
    return (db.select(db.levelSettings)..limit(1)).getSingleOrNull();
  }

  /// Update level settings
  Future<bool> updateLevelSettings(LevelSettingsCompanion entity) {
    return (db.update(db.levelSettings)..where((t) => t.id.equals(entity.id.value)))
        .write(entity)
        .then((rows) => rows > 0);
  }

  /// Initialize default level settings
  Future<void> initializeDefaultSettings() async {
    final settings = await getLevelSettings();

    if (settings == null) {
      await db.into(db.levelSettings).insert(
        LevelSettingsCompanion.insert(
          platinumPointsReq: 500,
          platinumBidReq: 0.95,
          platinumTripReq: 0.98,
          goldPointsReq: 350,
          goldBidReq: 0.90,
          goldTripReq: 0.95,
          silverPointsReq: 200,
          silverBidReq: 0.85,
          silverTripReq: 0.90,
        )
      );
    }
  }
}
