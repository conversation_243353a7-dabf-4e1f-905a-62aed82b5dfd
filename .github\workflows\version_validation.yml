name: Version Validation

on:
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'lib/core/version/**'
      - 'pubspec.yaml'
  push:
    branches: [ main, develop ]
    paths:
      - 'lib/core/version/**'
      - 'pubspec.yaml'

jobs:
  validate-versions:
    name: Validate App and Database Versions
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.19.0'
          channel: 'stable'
      
      - name: Install dependencies
        run: flutter pub get
      
      - name: Run version validation script
        run: dart .github/scripts/validate_versions.dart
