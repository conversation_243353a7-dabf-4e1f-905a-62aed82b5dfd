import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../config/supabase_config.dart';

/// Repository for handling authentication operations with Supabase
class AuthRepository {
  final SupabaseConfig _supabase = SupabaseConfig();

  /// Constructor
  AuthRepository();

  /// Get the current user
  User? get currentUser => _supabase.client.auth.currentUser;

  /// Check if user is authenticated
  bool get isAuthenticated => currentUser != null;

  /// Check if Supabase is initialized
  bool isSupabaseInitialized() {
    return _supabase.isInitialized;
  }

  /// Initialize the Supabase client
  Future<void> initialize() async {
    if (!_supabase.isInitialized) {
      await _supabase.initialize();
    }
  }

  /// Get the current session
  Session? getCurrentSession() {
    return _supabase.client.auth.currentSession;
  }

  /// Listen to auth state changes
  Stream<AuthState> get onAuthStateChange =>
      _supabase.client.auth.onAuthStateChange;

  /// Sign up with email and password
  Future<AuthResponse> signUp({
    required String email,
    required String password,
    Map<String, dynamic>? data,
  }) async {
    try {
      final response = await _supabase.client.auth.signUp(
        email: email,
        password: password,
        data: data,
      );

      debugPrint('User signed up: ${response.user?.email}');
      return response;
    } catch (e) {
      debugPrint('Error signing up: $e');
      rethrow;
    }
  }

  /// Sign in with email and password
  Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _supabase.client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      debugPrint('User signed in: ${response.user?.email}');
      return response;
    } catch (e) {
      debugPrint('Error signing in: $e');
      rethrow;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      await _supabase.client.auth.signOut();
      debugPrint('User signed out');
    } catch (e) {
      debugPrint('Error signing out: $e');
      rethrow;
    }
  }

  /// Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _supabase.client.auth.resetPasswordForEmail(email);
      debugPrint('Password reset email sent to $email');
    } catch (e) {
      debugPrint('Error resetting password: $e');
      rethrow;
    }
  }

  /// Refresh the current session
  Future<Session?> refreshSession() async {
    try {
      if (!_supabase.isInitialized) {
        debugPrint('Supabase not initialized, initializing before refresh');
        await initialize();
      }

      final currentSession = _supabase.client.auth.currentSession;
      if (currentSession == null) {
        debugPrint('No session to refresh - currentSession is null');
        return null;
      }

      // Log detailed session information
      final user = currentSession.user;
      final accessToken = currentSession.accessToken;
      final refreshToken = currentSession.refreshToken;
      final expiresAt = DateTime.fromMillisecondsSinceEpoch(
          currentSession.expiresAt! * 1000);
      final now = DateTime.now();
      final timeUntilExpiry = expiresAt.difference(now);

      // Always attempt to refresh if we have a refresh token, regardless of expiry time
      // This ensures we maintain a valid session even if the token is not yet close to expiry
      debugPrint('Current session details:');
      debugPrint('  User: ${user.email}');
      debugPrint('  User ID: ${user.id}');
      debugPrint('  Expires at: ${expiresAt.toIso8601String()}');
      debugPrint('  Time until expiry: ${timeUntilExpiry.inMinutes} minutes');
      debugPrint('  Has access token: ${accessToken.isNotEmpty}');
      debugPrint('  Has refresh token: ${refreshToken?.isNotEmpty ?? false}');

      // Check if we have a refresh token before attempting to refresh
      if (refreshToken == null || refreshToken.isEmpty) {
        debugPrint('No refresh token available, cannot refresh session');
        return currentSession;
      }

      try {
        debugPrint('Attempting to refresh session');

        // Use the refreshSession method with the current refresh token
        final response = await _supabase.client.auth.refreshSession();

        if (response.session != null) {
          final newExpiresAt = DateTime.fromMillisecondsSinceEpoch(
              response.session!.expiresAt! * 1000);
          final newTimeUntilExpiry = newExpiresAt.difference(now);

          debugPrint('Session refreshed successfully:');
          debugPrint('  New expires at: ${newExpiresAt.toIso8601String()}');
          debugPrint('  New time until expiry: ${newTimeUntilExpiry.inMinutes} minutes');
          debugPrint('  New access token available: ${response.session!.accessToken.isNotEmpty}');
          debugPrint('  New refresh token available: ${response.session!.refreshToken?.isNotEmpty ?? false}');

          return response.session;
        } else {
          debugPrint('Session refresh returned null session');

          // If refresh fails but current session is still valid, return it
          if (timeUntilExpiry.isNegative) {
            debugPrint('Current session has expired, cannot use it');
            return null;
          } else {
            debugPrint('Using current session as fallback');
            return currentSession;
          }
        }
      } catch (refreshError) {
        debugPrint('Error during refresh operation: $refreshError');

        // If refresh fails but current session is still valid, return it
        if (timeUntilExpiry.isNegative) {
          debugPrint('Current session has expired, cannot use it despite refresh error');
          return null;
        } else {
          debugPrint('Using current session as fallback despite refresh error');
          return currentSession;
        }
      }
    } catch (e) {
      debugPrint('Error in refreshSession method: $e');
      debugPrint('Stack trace: ${StackTrace.current}');
      return null;
    }
  }
}
