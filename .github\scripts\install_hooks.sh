#!/bin/sh
#
# <PERSON><PERSON>t to install git hooks
#

# Make sure we're in the project root
if [ ! -d ".git" ]; then
  echo "Error: This script must be run from the project root directory."
  exit 1
fi

# Create hooks directory if it doesn't exist
mkdir -p .git/hooks

# Copy pre-commit hook
cp .github/hooks/pre-commit .git/hooks/
chmod +x .git/hooks/pre-commit

# Copy commit-msg hook
cp .github/hooks/commit-msg .git/hooks/
chmod +x .git/hooks/commit-msg

echo "Git hooks installed successfully!"
echo "Installed hooks:"
echo "- pre-commit: Validates version consistency and suggests conventional commit format"
echo "- commit-msg: Enforces conventional commit format"
echo ""
echo "To learn more about conventional commits, see docs/auto_versioning.md"
