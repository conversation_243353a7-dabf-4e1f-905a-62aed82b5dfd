import 'dart:io';

/// Commit type enum representing different types of changes
enum CommitType {
  feat,    // New feature (minor version bump)
  fix,     // Bug fix (patch version bump)
  docs,    // Documentation changes (no version bump)
  style,   // Code style changes (no version bump)
  refactor,// Code refactoring (no version bump)
  perf,    // Performance improvements (patch version bump)
  test,    // Adding or fixing tests (no version bump)
  build,   // Build system changes (no version bump)
  ci,      // CI configuration changes (no version bump)
  chore,   // Other changes (no version bump)
  breaking,// Breaking changes (major version bump)
}

/// Version bump type enum
enum BumpType {
  none,
  patch,
  minor,
  major,
}

/// Represents a parsed commit
class Commit {
  final String hash;
  final String message;
  final String author;
  final DateTime date;
  final CommitType type;
  final String scope;
  final String description;
  final bool isBreaking;

  Commit({
    required this.hash,
    required this.message,
    required this.author,
    required this.date,
    required this.type,
    required this.scope,
    required this.description,
    required this.isBreaking,
  });

  @override
  String toString() {
    return 'Commit{hash: $hash, type: $type, scope: $scope, description: $description, isBreaking: $isBreaking}';
  }
}

/// Analyzes commits since the last version tag and determines the appropriate version bump
Future<BumpType> analyzeCommits() async {
  // Get the latest version tag
  final latestTagResult = await Process.run('git', ['describe', '--tags', '--abbrev=0']);
  
  if (latestTagResult.exitCode != 0) {
    print('No tags found. Assuming this is the first version.');
    // If no tags found, suggest a patch bump for the first version
    return BumpType.patch;
  }
  
  final latestTag = (latestTagResult.stdout as String).trim();
  print('Latest tag: $latestTag');
  
  // Get commits since the latest tag
  final commitsResult = await Process.run(
    'git', 
    ['log', '$latestTag..HEAD', '--pretty=format:%H|%an|%ad|%s', '--date=iso']
  );
  
  if (commitsResult.exitCode != 0) {
    print('Error getting commits: ${commitsResult.stderr}');
    return BumpType.none;
  }
  
  final commitsOutput = (commitsResult.stdout as String).trim();
  if (commitsOutput.isEmpty) {
    print('No new commits since last tag.');
    return BumpType.none;
  }
  
  final commitLines = commitsOutput.split('\n');
  final commits = <Commit>[];
  
  for (final line in commitLines) {
    final parts = line.split('|');
    if (parts.length < 4) continue;
    
    final hash = parts[0];
    final author = parts[1];
    final dateStr = parts[2];
    final message = parts[3];
    
    final date = DateTime.parse(dateStr);
    
    // Parse conventional commit format
    final commit = parseCommitMessage(hash, message, author, date);
    commits.add(commit);
  }
  
  print('Found ${commits.length} commits since last tag.');
  
  // Determine bump type based on commits
  return determineBumpType(commits);
}

/// Parses a commit message according to conventional commits format
Commit parseCommitMessage(String hash, String message, String author, DateTime date) {
  // Default values
  CommitType type = CommitType.chore;
  String scope = '';
  String description = message;
  bool isBreaking = false;
  
  // Check for breaking change indicator
  if (message.contains('BREAKING CHANGE:') || message.contains('!:')) {
    isBreaking = true;
  }
  
  // Parse conventional commit format: type(scope): description
  final conventionalPattern = RegExp(r'^(\w+)(?:\(([^)]*)\))?(!)?:\s*(.*)$');
  final match = conventionalPattern.firstMatch(message);
  
  if (match != null) {
    final typeStr = match.group(1)?.toLowerCase();
    scope = match.group(2) ?? '';
    final breakingMarker = match.group(3);
    description = match.group(4) ?? description;
    
    // Set breaking flag if ! is present
    if (breakingMarker == '!') {
      isBreaking = true;
    }
    
    // Map string to CommitType enum
    if (typeStr != null) {
      try {
        type = CommitType.values.firstWhere(
          (t) => t.name == typeStr,
          orElse: () => CommitType.chore,
        );
      } catch (_) {
        // If not found, default to chore
        type = CommitType.chore;
      }
    }
  }
  
  return Commit(
    hash: hash,
    message: message,
    author: author,
    date: date,
    type: type,
    scope: scope,
    description: description,
    isBreaking: isBreaking,
  );
}

/// Determines the appropriate version bump based on the commits
BumpType determineBumpType(List<Commit> commits) {
  bool hasMajor = false;
  bool hasMinor = false;
  bool hasPatch = false;
  
  for (final commit in commits) {
    // Breaking changes trigger a major version bump
    if (commit.isBreaking) {
      hasMajor = true;
      break;
    }
    
    // Features trigger a minor version bump
    if (commit.type == CommitType.feat) {
      hasMinor = true;
    }
    
    // Fixes and performance improvements trigger a patch version bump
    if (commit.type == CommitType.fix || commit.type == CommitType.perf) {
      hasPatch = true;
    }
  }
  
  if (hasMajor) return BumpType.major;
  if (hasMinor) return BumpType.minor;
  if (hasPatch) return BumpType.patch;
  
  return BumpType.none;
}

/// Gets all commits since the last version tag for changelog generation
Future<List<Commit>> getCommitsSinceLastTag() async {
  // Get the latest version tag
  final latestTagResult = await Process.run('git', ['describe', '--tags', '--abbrev=0']);
  
  String range;
  if (latestTagResult.exitCode != 0) {
    print('No tags found. Getting all commits.');
    range = 'HEAD';
  } else {
    final latestTag = (latestTagResult.stdout as String).trim();
    range = '$latestTag..HEAD';
  }
  
  // Get commits since the latest tag
  final commitsResult = await Process.run(
    'git', 
    ['log', range, '--pretty=format:%H|%an|%ad|%s', '--date=iso']
  );
  
  if (commitsResult.exitCode != 0) {
    print('Error getting commits: ${commitsResult.stderr}');
    return [];
  }
  
  final commitsOutput = (commitsResult.stdout as String).trim();
  if (commitsOutput.isEmpty) {
    return [];
  }
  
  final commitLines = commitsOutput.split('\n');
  final commits = <Commit>[];
  
  for (final line in commitLines) {
    final parts = line.split('|');
    if (parts.length < 4) continue;
    
    final hash = parts[0];
    final author = parts[1];
    final dateStr = parts[2];
    final message = parts[3];
    
    final date = DateTime.parse(dateStr);
    
    // Parse conventional commit format
    final commit = parseCommitMessage(hash, message, author, date);
    commits.add(commit);
  }
  
  return commits;
}
