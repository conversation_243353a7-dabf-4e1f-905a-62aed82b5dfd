import 'package:drift/drift.dart';
import '../datasources/app_database.dart';
import 'database_repository.dart';

/// Repository for Income table operations
class IncomeRepository extends BaseDatabaseRepository<IncomeData, IncomeCompanion> {
  IncomeRepository(AppDatabase db) : super(db, db.income);

  @override
  Future<List<IncomeData>> getAll() {
    return (db.select(db.income)
      ..where((t) => t.deletedAt.isNull())
      ..orderBy([(t) => OrderingTerm.desc(t.date)])
    ).get();
  }

  @override
  Future<IncomeData?> getById(int id) {
    return (db.select(db.income)
      ..where((t) => t.id.equals(id))
      ..where((t) => t.deletedAt.isNull())
    ).getSingleOrNull();
  }

  @override
  Future<bool> update(IncomeData entity) {
    return db.update(db.income).replace(entity);
  }

  @override
  Future<int> delete(int id) {
    return (db.delete(db.income)..where((t) => t.id.equals(id))).go();
  }

  @override
  Future<List<IncomeData>> getUnsyncedRecords() {
    return (db.select(db.income)
      ..where((t) => t.syncStatus.equals('pendingUpload'))
    ).get();
  }

  @override
  Future<bool> updateWithSync(IncomeData entity) async {
    // First update the entity
    final bool updated = await update(entity);

    // Then separately update the sync status fields if the entity update succeeded
    if (updated) {
      await db.customUpdate(
        'UPDATE income SET updated_at = ?, sync_status = ? WHERE id = ?',
        variables: [
          Variable(DateTime.now().toUtc().toIso8601String()),
          const Variable('pendingUpload'),
          Variable(entity.id),
        ],
        updateKind: UpdateKind.update,
      );
    }

    return updated;
  }

  @override
  Future<bool> softDelete(int id) async {
    return (db.update(db.income)..where((t) => t.id.equals(id))).write(
      IncomeCompanion(
        deletedAt: Value(DateTime.now().toUtc()),
        syncStatus: const Value(SyncStatus.pendingUpload),
      ),
    ).then((rows) => rows > 0);
  }

  @override
  Future<void> markAsSynced(String uuid) async {
    await (db.update(db.income)..where((t) => t.uuid.equals(uuid))).write(
      const IncomeCompanion(
        syncStatus: Value(SyncStatus.synced),
      ),
    );
  }



  /// Get income records for a specific date range
  Future<List<IncomeData>> getIncomeForDateRange(DateTime start, DateTime end) {
    return (db.select(db.income)
      ..where((t) => t.deletedAt.isNull())
      ..where((t) => t.date.isBiggerOrEqualValue(start))
      ..where((t) => t.date.isSmallerOrEqualValue(end))
      ..orderBy([(t) => OrderingTerm.desc(t.date)])
    ).get();
  }

  /// Get the latest income record
  Future<IncomeData?> getLatestIncome() {
    return (db.select(db.income)
      ..where((t) => t.deletedAt.isNull())
      ..orderBy([(t) => OrderingTerm.desc(t.date)])
      ..limit(1)
    ).getSingleOrNull();
  }

  /// Get the highest mileage value from all income records
  Future<int> getHighestMileage() async {
    final result = await db.customSelect(
      'SELECT MAX(final_mileage) as max_mileage FROM income WHERE deleted_at IS NULL',
    ).getSingleOrNull();

    return result?.data['max_mileage'] as int? ?? 0;
  }

  /// Check if an income record already exists for a specific date
  Future<bool> checkDateExists(DateTime date, {int? excludeId}) async {
    final query = db.select(db.income)
      ..where((t) => t.deletedAt.isNull())
      ..where((t) => t.date.equals(date));

    if (excludeId != null) {
      query.where((t) => t.id.isNotValue(excludeId));
    }

    final count = await query.get().then((records) => records.length);
    return count > 0;
  }
}
