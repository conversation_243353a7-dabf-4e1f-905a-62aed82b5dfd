plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace = "com.englr18.kiq"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.englr18.kiq"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.debug

            // Disable R8 for troubleshooting build issues
            minifyEnabled = false
            shrinkResources = false
        }
    }

    // Custom APK naming configuration
    applicationVariants.all { variant ->
        variant.outputs.all { output ->
            // Use the app name and version for the APK filename
            def appName = "kiq"
            def versionName = variant.versionName
            def buildType = variant.buildType.name
            def buildNumber = variant.versionCode

            // Set the output file name
            outputFileName = "${appName}-v${versionName}-b${buildNumber}-${buildType}.apk"
        }
    }

    // Add a task to copy the renamed APK to the flutter-apk directory
    tasks.whenTaskAdded { task ->
        if (task.name == 'assembleRelease') {
            task.doLast {
                copy {
                    from "${project.buildDir}/outputs/apk/release/"
                    include "kiq-v*-release.apk"
                    into "${project.buildDir}/outputs/flutter-apk/"
                    rename { String fileName ->
                        // Keep a copy with the custom name in flutter-apk directory
                        fileName
                    }
                }

                // Also create a copy with the standard name that Flutter expects
                copy {
                    from "${project.buildDir}/outputs/apk/release/"
                    include "kiq-v*-release.apk"
                    into "${project.buildDir}/outputs/flutter-apk/"
                    rename { String fileName ->
                        // Also create the standard app-release.apk that Flutter expects
                        "app-release.apk"
                    }
                }

                println "✓ Custom named APK copied to flutter-apk directory"
            }
        }

        if (task.name == 'assembleDebug') {
            task.doLast {
                copy {
                    from "${project.buildDir}/outputs/apk/debug/"
                    include "kiq-v*-debug.apk"
                    into "${project.buildDir}/outputs/flutter-apk/"
                    rename { String fileName ->
                        // Keep a copy with the custom name in flutter-apk directory
                        fileName
                    }
                }

                // Also create a copy with the standard name that Flutter expects
                copy {
                    from "${project.buildDir}/outputs/apk/debug/"
                    include "kiq-v*-debug.apk"
                    into "${project.buildDir}/outputs/flutter-apk/"
                    rename { String fileName ->
                        // Also create the standard app-debug.apk that Flutter expects
                        "app-debug.apk"
                    }
                }

                println "✓ Custom named APK copied to flutter-apk directory"
            }
        }

        if (task.name == 'assembleProfile') {
            task.doLast {
                copy {
                    from "${project.buildDir}/outputs/apk/profile/"
                    include "kiq-v*-profile.apk"
                    into "${project.buildDir}/outputs/flutter-apk/"
                    rename { String fileName ->
                        // Keep a copy with the custom name in flutter-apk directory
                        fileName
                    }
                }

                // Also create a copy with the standard name that Flutter expects
                copy {
                    from "${project.buildDir}/outputs/apk/profile/"
                    include "kiq-v*-profile.apk"
                    into "${project.buildDir}/outputs/flutter-apk/"
                    rename { String fileName ->
                        // Also create the standard app-profile.apk that Flutter expects
                        "app-profile.apk"
                    }
                }

                println "✓ Custom named APK copied to flutter-apk directory"
            }
        }
    }
}

flutter {
    source = "../.."
}
