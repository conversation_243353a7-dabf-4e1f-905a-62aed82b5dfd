# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: Project
# Configurations: RelWithDebInfo
# =============================================================================
# =============================================================================

#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = /home/<USER>/Android/Sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/home/<USER>/flutter/packages/flutter_tools/gradle/src/main/groovy -B"/media/ceko/Seagate Backup Plus Drive/DEV/englr/kiq/android/app/.cxx/RelWithDebInfo/2z3v5t55/x86_64"
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = /home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = /home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja -t targets
  description = All primary targets available:

