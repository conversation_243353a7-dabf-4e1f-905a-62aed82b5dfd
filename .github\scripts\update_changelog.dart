import 'dart:io';
import 'analyze_commits.dart';

/// Updates the CHANGELOG.md file with new entries from commits
Future<void> updateChangelog(List<Commit> commits, String version) async {
  if (commits.isEmpty) {
    print('No commits to add to changelog.');
    return;
  }
  
  final changelogFile = File('CHANGELOG.md');
  if (!changelogFile.existsSync()) {
    print('CHANGELOG.md not found. Creating a new one.');
    await changelogFile.writeAsString('''# Changelog

All notable changes to the kiQ application will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

''');
  }
  
  String changelogContent = await changelogFile.readAsString();
  
  // Group commits by type
  final features = <Commit>[];
  final fixes = <Commit>[];
  final performance = <Commit>[];
  final breaking = <Commit>[];
  final other = <Commit>[];
  
  for (final commit in commits) {
    if (commit.isBreaking) {
      breaking.add(commit);
    } else if (commit.type == CommitType.feat) {
      features.add(commit);
    } else if (commit.type == CommitType.fix) {
      fixes.add(commit);
    } else if (commit.type == CommitType.perf) {
      performance.add(commit);
    } else if (commit.type != CommitType.docs && 
               commit.type != CommitType.style && 
               commit.type != CommitType.test && 
               commit.type != CommitType.ci) {
      other.add(commit);
    }
  }
  
  // Format the new changelog section
  final now = DateTime.now();
  final dateStr = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
  
  StringBuffer newSection = StringBuffer();
  newSection.writeln('## [$version] - $dateStr');
  newSection.writeln();
  
  if (breaking.isNotEmpty) {
    newSection.writeln('### Breaking Changes');
    for (final commit in breaking) {
      newSection.writeln('- ${commit.description} (${commit.hash.substring(0, 7)})');
    }
    newSection.writeln();
  }
  
  if (features.isNotEmpty) {
    newSection.writeln('### Added');
    for (final commit in features) {
      final scopeText = commit.scope.isNotEmpty ? '[${commit.scope}] ' : '';
      newSection.writeln('- $scopeText${commit.description} (${commit.hash.substring(0, 7)})');
    }
    newSection.writeln();
  }
  
  if (fixes.isNotEmpty) {
    newSection.writeln('### Fixed');
    for (final commit in fixes) {
      final scopeText = commit.scope.isNotEmpty ? '[${commit.scope}] ' : '';
      newSection.writeln('- $scopeText${commit.description} (${commit.hash.substring(0, 7)})');
    }
    newSection.writeln();
  }
  
  if (performance.isNotEmpty) {
    newSection.writeln('### Performance');
    for (final commit in performance) {
      final scopeText = commit.scope.isNotEmpty ? '[${commit.scope}] ' : '';
      newSection.writeln('- $scopeText${commit.description} (${commit.hash.substring(0, 7)})');
    }
    newSection.writeln();
  }
  
  if (other.isNotEmpty) {
    newSection.writeln('### Changed');
    for (final commit in other) {
      final typeText = '[${commit.type.name}]';
      final scopeText = commit.scope.isNotEmpty ? '[${commit.scope}] ' : '';
      newSection.writeln('- $typeText $scopeText${commit.description} (${commit.hash.substring(0, 7)})');
    }
    newSection.writeln();
  }
  
  // Insert the new section after the header
  final unreleasedIndex = changelogContent.indexOf('## [Unreleased]');
  if (unreleasedIndex >= 0) {
    // If there's an Unreleased section, add after it
    final nextSectionIndex = changelogContent.indexOf('## [', unreleasedIndex + 1);
    if (nextSectionIndex >= 0) {
      changelogContent = changelogContent.substring(0, nextSectionIndex) + 
                         newSection.toString() + 
                         changelogContent.substring(nextSectionIndex);
    } else {
      changelogContent = changelogContent + '\n' + newSection.toString();
    }
  } else {
    // If there's no Unreleased section, add at the beginning after the header
    final headerEndIndex = changelogContent.indexOf('\n\n') + 2;
    changelogContent = changelogContent.substring(0, headerEndIndex) + 
                       '## [Unreleased]\n\n' +
                       newSection.toString() + 
                       changelogContent.substring(headerEndIndex);
  }
  
  // Write the updated changelog
  await changelogFile.writeAsString(changelogContent);
  print('Updated CHANGELOG.md with new version $version');
}
