// Version update script
// This script updates the version in version_info.dart and pubspec.yaml

import 'dart:io';

void main(List<String> args) {
  print('Starting version update...');
  
  if (args.isEmpty) {
    print('Usage: dart update_version.dart [patch|minor|major] [build_number]');
    exit(1);
  }
  
  final versionType = args[0];
  final customBuildNumber = args.length > 1 ? args[1] : '';
  
  if (!['patch', 'minor', 'major'].contains(versionType)) {
    print('Invalid version type: $versionType. Must be one of: patch, minor, major');
    exit(1);
  }
  
  // Read version files
  final versionInfoFile = File('lib/core/version/version_info.dart');
  final pubspecFile = File('pubspec.yaml');
  
  if (!versionInfoFile.existsSync()) {
    print('Error: version_info.dart file not found');
    exit(1);
  }
  
  if (!pubspecFile.existsSync()) {
    print('Error: pubspec.yaml file not found');
    exit(1);
  }
  
  // Read file contents
  final versionInfoContent = versionInfoFile.readAsStringSync();
  final pubspecContent = pubspecFile.readAsStringSync();
  
  // Extract current version from version_info.dart
  final versionMatch = RegExp(r'static const String version = [\'"]([^\'"]+)[\'"]').firstMatch(versionInfoContent);
  final buildNumberMatch = RegExp(r'static const int buildNumber = (\d+)').firstMatch(versionInfoContent);
  
  if (versionMatch == null || buildNumberMatch == null) {
    print('Error: Could not extract version information from version_info.dart');
    exit(1);
  }
  
  final currentVersion = versionMatch.group(1)!;
  final currentBuildNumber = int.parse(buildNumberMatch.group(1)!);
  
  print('Current version: $currentVersion+$currentBuildNumber');
  
  // Calculate new version
  final versionParts = currentVersion.split('.');
  if (versionParts.length != 3) {
    print('Error: Current version does not follow semantic versioning (MAJOR.MINOR.PATCH)');
    exit(1);
  }
  
  int major = int.parse(versionParts[0]);
  int minor = int.parse(versionParts[1]);
  int patch = int.parse(versionParts[2]);
  
  switch (versionType) {
    case 'major':
      major++;
      minor = 0;
      patch = 0;
      break;
    case 'minor':
      minor++;
      patch = 0;
      break;
    case 'patch':
      patch++;
      break;
  }
  
  final newVersion = '$major.$minor.$patch';
  
  // Calculate new build number
  final newBuildNumber = customBuildNumber.isNotEmpty 
      ? int.parse(customBuildNumber) 
      : currentBuildNumber + 1;
  
  print('New version: $newVersion+$newBuildNumber');
  
  // Update version_info.dart
  final updatedVersionInfoContent = versionInfoContent
      .replaceFirst(
        RegExp(r'static const String version = [\'"]([^\'"]+)[\'"]'), 
        'static const String version = \'$newVersion\''
      )
      .replaceFirst(
        RegExp(r'static const int buildNumber = (\d+)'), 
        'static const int buildNumber = $newBuildNumber'
      );
  
  versionInfoFile.writeAsStringSync(updatedVersionInfoContent);
  print('Updated version_info.dart');
  
  // Update pubspec.yaml
  final updatedPubspecContent = pubspecContent
      .replaceFirst(
        RegExp(r'version: ([^\s]+)'), 
        'version: $newVersion+$newBuildNumber'
      );
  
  pubspecFile.writeAsStringSync(updatedPubspecContent);
  print('Updated pubspec.yaml');
  
  // Check if we need to update database_version_manager.dart
  final dbVersionManagerFile = File('lib/core/version/database_version_manager.dart');
  
  if (dbVersionManagerFile.existsSync()) {
    final dbVersionManagerContent = dbVersionManagerFile.readAsStringSync();
    
    // Check if the new version is already in the version map
    final versionMapRegex = RegExp(r'static const Map<String, int> _versionMap = \{([^}]+)\}', dotAll: true);
    final versionMapMatch = versionMapRegex.firstMatch(dbVersionManagerContent);
    
    if (versionMapMatch != null) {
      final versionMapContent = versionMapMatch.group(1)!;
      final newVersionEntry = "'$newVersion':";
      
      if (!versionMapContent.contains(newVersionEntry)) {
        print('Warning: New version $newVersion not found in database_version_manager.dart');
        print('You may need to update the _versionMap in database_version_manager.dart');
      }
    }
  }
  
  // Output for GitHub Actions
  print('::set-output name=new_version::$newVersion');
  print('::set-output name=new_build_number::$newBuildNumber');
  
  print('Version update completed successfully!');
}
