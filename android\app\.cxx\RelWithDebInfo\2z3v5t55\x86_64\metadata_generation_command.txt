                        -H/home/<USER>/flutter/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=/home/<USER>/Android/Sdk/ndk/26.3.11579264
-DCMAKE_ANDROID_NDK=/home/<USER>/Android/Sdk/ndk/26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=/home/<USER>/Android/Sdk/ndk/26.3.11579264/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/media/ceko/Seagate Backup Plus Drive/DEV/englr/kiq/build/app/intermediates/cxx/RelWithDebInfo/2z3v5t55/obj/x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/media/ceko/Seagate Backup Plus Drive/DEV/englr/kiq/build/app/intermediates/cxx/RelWithDebInfo/2z3v5t55/obj/x86_64
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-B/media/ceko/Seagate Backup Plus Drive/DEV/englr/kiq/android/app/.cxx/RelWithDebInfo/2z3v5t55/x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2