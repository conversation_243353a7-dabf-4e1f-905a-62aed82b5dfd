#!/bin/sh
#
# Pre-commit hook to validate version consistency and commit message format
#
# To enable this hook, copy it to .git/hooks/pre-commit

# Check if any version-related files are being committed
version_files_changed=$(git diff --cached --name-only | grep -E 'lib/core/version/|pubspec.yaml')

if [ -n "$version_files_changed" ]; then
  echo "Version-related files changed. Validating versions..."

  # Run the version validation script
  dart .github/scripts/validate_versions.dart

  # Check the exit code
  if [ $? -ne 0 ]; then
    echo "Version validation failed. Please fix the issues before committing."
    exit 1
  fi

  echo "Version validation passed."
fi

# Check commit message format (for interactive commits)
if [ -f .git/COMMIT_EDITMSG ]; then
  commit_msg=$(cat .git/COMMIT_EDITMSG)

  # Skip merge commits
  if [[ $commit_msg == Merge* ]]; then
    exit 0
  fi

  # Check for conventional commit format
  if ! echo "$commit_msg" | grep -qE '^(feat|fix|docs|style|refactor|perf|test|build|ci|chore)(\([a-z0-9-]+\))?!?: .+'; then
    echo "Warning: Commit message does not follow conventional commits format."
    echo "Recommended format: <type>(<scope>): <description>"
    echo "Examples:"
    echo "  feat(auth): add biometric authentication"
    echo "  fix(sync): resolve data synchronization issue"
    echo "  docs: update README with new instructions"
    echo ""
    echo "Types: feat, fix, docs, style, refactor, perf, test, build, ci, chore"
    echo "Add ! before : for breaking changes, e.g., feat!: breaking change"
    echo ""
    echo "Press Ctrl+C to cancel commit and fix the message, or press Enter to continue anyway."
    read -r
  fi
fi

exit 0
