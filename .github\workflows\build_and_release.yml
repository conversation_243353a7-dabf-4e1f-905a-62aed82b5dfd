name: Build and Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version_type:
        description: 'Type of version update'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major
      build_number:
        description: 'Build number (leave empty to auto-increment)'
        required: false
        type: string

jobs:
  update-version:
    name: Update Version
    runs-on: ubuntu-latest
    outputs:
      new_version: ${{ steps.update_version.outputs.new_version }}
      new_build_number: ${{ steps.update_version.outputs.new_build_number }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.19.0'
          channel: 'stable'
      
      - name: Install dependencies
        run: flutter pub get
      
      - name: Update version
        id: update_version
        run: dart .github/scripts/update_version.dart ${{ github.event.inputs.version_type || 'patch' }} ${{ github.event.inputs.build_number || '' }}
      
      - name: Commit version changes
        if: github.event_name == 'workflow_dispatch'
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add lib/core/version/version_info.dart pubspec.yaml
          git commit -m "Bump version to ${{ steps.update_version.outputs.new_version }}+${{ steps.update_version.outputs.new_build_number }}"
          git tag -a "v${{ steps.update_version.outputs.new_version }}" -m "Version ${{ steps.update_version.outputs.new_version }}"
          git push origin HEAD:${{ github.ref }} --tags
  
  validate-versions:
    name: Validate Versions
    needs: update-version
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        if: github.event_name == 'push'
      
      - name: Checkout code with ref
        uses: actions/checkout@v3
        if: github.event_name == 'workflow_dispatch'
        with:
          ref: ${{ github.ref }}
      
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.19.0'
          channel: 'stable'
      
      - name: Install dependencies
        run: flutter pub get
      
      - name: Run version validation script
        run: dart .github/scripts/validate_versions.dart
  
  build-android:
    name: Build Android
    needs: [update-version, validate-versions]
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        if: github.event_name == 'push'
      
      - name: Checkout code with ref
        uses: actions/checkout@v3
        if: github.event_name == 'workflow_dispatch'
        with:
          ref: ${{ github.ref }}
      
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.19.0'
          channel: 'stable'
      
      - name: Install dependencies
        run: flutter pub get
      
      - name: Build APK
        run: flutter build apk --release
      
      - name: Upload APK
        uses: actions/upload-artifact@v3
        with:
          name: app-release
          path: build/app/outputs/flutter-apk/app-release.apk
  
  create-release:
    name: Create Release
    needs: [update-version, build-android]
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch' || startsWith(github.ref, 'refs/tags/')
    
    steps:
      - name: Download APK
        uses: actions/download-artifact@v3
        with:
          name: app-release
          path: artifacts
      
      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ github.event_name == 'workflow_dispatch' && format('v{0}', needs.update-version.outputs.new_version) || github.ref_name }}
          name: ${{ github.event_name == 'workflow_dispatch' && format('Release v{0}', needs.update-version.outputs.new_version) || format('Release {0}', github.ref_name) }}
          draft: true
          prerelease: false
          files: |
            artifacts/app-release.apk
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
