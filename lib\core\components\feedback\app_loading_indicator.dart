import 'package:flutter/material.dart';
import '../../theme/design_tokens.dart';
import '../../utils/text_style_helper.dart';

/// Loading indicator types available in the application
enum AppLoadingIndicatorType {
  /// Circular loading indicator
  circular,

  /// Linear loading indicator
  linear,

  /// Overlay loading indicator (covers the entire screen)
  overlay
}

/// Loading indicator sizes available in the application
enum AppLoadingIndicatorSize {
  /// Small loading indicator
  small,

  /// Medium loading indicator (default)
  medium,

  /// Large loading indicator
  large
}

/// A reusable loading indicator component that follows the design system
///
/// This loading indicator adapts its appearance based on the specified type and size,
/// ensuring consistency across the application.
class AppLoadingIndicator extends StatelessWidget {
  /// The type of loading indicator to display
  final AppLoadingIndicatorType type;

  /// The size of the loading indicator
  final AppLoadingIndicatorSize size;

  /// Optional color for the loading indicator
  final Color? color;

  /// Optional background color for the loading indicator
  final Color? backgroundColor;

  /// Optional message to display with the loading indicator
  final String? message;

  /// Whether to show the loading indicator with a transparent background
  final bool transparent;

  /// Optional value for determinate loading indicators (0.0 to 1.0)
  final double? value;

  /// Creates a loading indicator with the specified parameters
  const AppLoadingIndicator({
    Key? key,
    this.type = AppLoadingIndicatorType.circular,
    this.size = AppLoadingIndicatorSize.medium,
    this.color,
    this.backgroundColor,
    this.message,
    this.transparent = false,
    this.value,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Determine indicator color
    final Color indicatorColor = color ?? DesignTokens.colorPrimary;

    // Determine indicator size
    final double indicatorSize = _getIndicatorSize();

    // Determine stroke width
    final double strokeWidth = _getStrokeWidth();

    // Create the appropriate indicator based on type
    Widget indicator;
    switch (type) {
      case AppLoadingIndicatorType.linear:
        indicator = LinearProgressIndicator(
          value: value,
          backgroundColor: backgroundColor ?? DesignTokens.colorPrimaryLight.withAlpha(77),
          color: indicatorColor,
          minHeight: DesignTokens.heightProgressIndicator,
        );
        break;
      case AppLoadingIndicatorType.overlay:
        return _buildOverlayIndicator(context, indicatorColor, indicatorSize, strokeWidth);
      case AppLoadingIndicatorType.circular:
        indicator = SizedBox(
          width: indicatorSize,
          height: indicatorSize,
          child: CircularProgressIndicator(
            value: value,
            backgroundColor: backgroundColor,
            color: indicatorColor,
            strokeWidth: strokeWidth,
          ),
        );
        break;
    }

    // If there's a message, show it with the indicator
    if (message != null && message!.isNotEmpty) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          indicator,
          SizedBox(height: DesignTokens.spacingMd),
          Text(
            message!,
            style: TextStyleHelper.bodyMedium(context),
            textAlign: TextAlign.center,
          ),
        ],
      );
    }

    return indicator;
  }

  /// Builds an overlay loading indicator
  Widget _buildOverlayIndicator(
    BuildContext context,
    Color indicatorColor,
    double indicatorSize,
    double strokeWidth,
  ) {
    return Container(
      color: transparent
          ? Colors.transparent
          : Colors.black.withAlpha(128),
      child: Center(
        child: Container(
          padding: EdgeInsets.all(DesignTokens.spacingLg),
          decoration: BoxDecoration(
            color: DesignTokens.colorSurface,
            borderRadius: BorderRadius.circular(DesignTokens.radiusCard),
            boxShadow: [
              BoxShadow(
                color: DesignTokens.colorShadow,
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: indicatorSize,
                height: indicatorSize,
                child: CircularProgressIndicator(
                  value: value,
                  backgroundColor: backgroundColor,
                  color: indicatorColor,
                  strokeWidth: strokeWidth,
                ),
              ),
              if (message != null && message!.isNotEmpty) ...[
                SizedBox(height: DesignTokens.spacingMd),
                Text(
                  message!,
                  style: TextStyleHelper.bodyMedium(context),
                  textAlign: TextAlign.center,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Gets the appropriate indicator size based on the size parameter
  double _getIndicatorSize() {
    switch (size) {
      case AppLoadingIndicatorSize.small:
        return 16.0;
      case AppLoadingIndicatorSize.large:
        return 48.0;
      case AppLoadingIndicatorSize.medium:
        return 24.0;
    }
  }

  /// Gets the appropriate stroke width based on the size parameter
  double _getStrokeWidth() {
    switch (size) {
      case AppLoadingIndicatorSize.small:
        return 2.0;
      case AppLoadingIndicatorSize.large:
        return 4.0;
      case AppLoadingIndicatorSize.medium:
        return 3.0;
    }
  }
}
