import 'package:drift/drift.dart';
import '../datasources/app_database.dart';

/// Generic repository interface for database operations
///
/// This interface defines the common CRUD operations that all repositories should implement.
/// It is parameterized with the entity type [T] and the companion type [C] for inserts/updates.
abstract class DatabaseRepository<T, C> {
  /// Get all records that haven't been soft-deleted
  Future<List<T>> getAll();

  /// Get a record by its ID
  Future<T?> getById(int id);

  /// Insert a new record
  Future<int> insert(C entity);

  /// Update an existing record
  Future<bool> update(T entity);

  /// Update an existing record and mark it for sync
  Future<bool> updateWithSync(covariant Object entity);

  /// Hard delete a record (permanent deletion)
  Future<int> delete(int id);

  /// Soft delete a record (mark as deleted but keep in database)
  Future<bool> softDelete(int id);

  /// Get all records that are pending upload (for sync)
  Future<List<T>> getUnsyncedRecords();

  /// Mark a record as synced
  Future<void> markAsSynced(String uuid);
}

/// Base implementation of the DatabaseRepository interface
///
/// This class provides a base implementation of the DatabaseRepository interface
/// that can be extended by entity-specific repositories.
abstract class BaseDatabaseRepository<T extends DataClass, C extends Insertable<T>>
    implements DatabaseRepository<T, C> {
  final AppDatabase db;
  final TableInfo<Table, T> table;

  BaseDatabaseRepository(this.db, this.table);

  // Each entity-specific repository must implement these methods
  // since the base implementation can't access the table columns directly
  @override
  Future<List<T>> getAll();

  @override
  Future<T?> getById(int id);

  @override
  Future<int> insert(C entity) {
    return db.into(table).insert(entity);
  }

  @override
  Future<bool> update(T entity);

  @override
  Future<int> delete(int id);

  @override
  Future<List<T>> getUnsyncedRecords();
}
