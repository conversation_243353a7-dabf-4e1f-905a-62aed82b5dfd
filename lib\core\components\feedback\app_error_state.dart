import 'package:flutter/material.dart';
import '../../theme/design_tokens.dart';
import '../../utils/text_style_helper.dart';
import '../buttons/app_button.dart';

/// A reusable error state component that follows the design system
///
/// This error state adapts its appearance based on the specified parameters,
/// providing a consistent way to display error states across the application.
class AppErrorState extends StatelessWidget {
  /// The title to display in the error state
  final String title;
  
  /// Optional error message to display
  final String? message;
  
  /// Optional error object to display (for debugging)
  final Object? error;
  
  /// Optional stack trace to display (for debugging)
  final StackTrace? stackTrace;
  
  /// Optional retry button text
  final String? retryButtonText;
  
  /// Optional retry button callback
  final VoidCallback? onRetry;
  
  /// Optional icon to display in the error state
  final IconData icon;
  
  /// Optional color for the icon
  final Color? iconColor;
  
  /// Optional size for the icon
  final double? iconSize;
  
  /// Optional padding for the error state
  final EdgeInsetsGeometry? padding;
  
  /// Whether to show technical details (error and stack trace)
  final bool showTechnicalDetails;
  
  /// Whether to show a compact version of the error state
  final bool compact;

  /// Creates an error state with the specified parameters
  const AppErrorState({
    Key? key,
    required this.title,
    this.message,
    this.error,
    this.stackTrace,
    this.retryButtonText = 'Retry',
    this.onRetry,
    this.icon = Icons.error_outline,
    this.iconColor,
    this.iconSize,
    this.padding,
    this.showTechnicalDetails = false,
    this.compact = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? EdgeInsets.all(DesignTokens.spacingLg),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Icon
          Icon(
            icon,
            size: iconSize ?? (compact ? 48.0 : 64.0),
            color: iconColor ?? DesignTokens.colorError,
          ),
          
          // Spacing
          SizedBox(height: compact ? DesignTokens.spacingMd : DesignTokens.spacingLg),
          
          // Title
          Text(
            title,
            style: compact 
                ? TextStyleHelper.heading3(context)
                : TextStyleHelper.heading2(context),
            textAlign: TextAlign.center,
          ),
          
          // Message
          if (message != null && message!.isNotEmpty) ...[
            SizedBox(height: compact ? DesignTokens.spacingXs : DesignTokens.spacingMd),
            Text(
              message!,
              style: TextStyleHelper.bodyMedium(context),
              textAlign: TextAlign.center,
            ),
          ],
          
          // Technical details (for debugging)
          if (showTechnicalDetails && error != null) ...[
            SizedBox(height: DesignTokens.spacingMd),
            Container(
              padding: EdgeInsets.all(DesignTokens.spacingMd),
              decoration: BoxDecoration(
                color: DesignTokens.colorErrorLight,
                borderRadius: BorderRadius.circular(DesignTokens.radiusButton),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Error: ${error.toString()}',
                    style: TextStyleHelper.bodySmall(context).copyWith(
                      fontFamily: 'monospace',
                    ),
                  ),
                  if (stackTrace != null) ...[
                    SizedBox(height: DesignTokens.spacingXs),
                    Text(
                      'Stack Trace: ${_formatStackTrace(stackTrace.toString())}',
                      style: TextStyleHelper.bodySmall(context).copyWith(
                        fontFamily: 'monospace',
                      ),
                      maxLines: 5,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
          ],
          
          // Retry button
          if (onRetry != null) ...[
            SizedBox(height: compact ? DesignTokens.spacingMd : DesignTokens.spacingLg),
            AppButton(
              text: retryButtonText ?? 'Retry',
              onPressed: onRetry,
              type: AppButtonType.primary,
              size: compact ? AppButtonSize.small : AppButtonSize.medium,
              icon: Icons.refresh,
            ),
          ],
        ],
      ),
    );
  }

  /// Formats the stack trace for display
  String _formatStackTrace(String stackTrace) {
    // Limit to first 3 lines
    final lines = stackTrace.split('\n');
    if (lines.length > 3) {
      return '${lines.take(3).join('\n')}...';
    }
    return stackTrace;
  }
}
