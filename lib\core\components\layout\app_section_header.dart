import 'package:flutter/material.dart';
import '../../theme/design_tokens.dart';
import '../../utils/text_style_helper.dart';

/// A reusable section header component that follows the design system
///
/// This section header adapts its appearance based on the specified parameters,
/// providing a consistent way to label sections across the application.
class AppSectionHeader extends StatelessWidget {
  /// The title to display in the section header
  final String title;
  
  /// Optional subtitle to display in the section header
  final String? subtitle;
  
  /// Optional action button text
  final String? actionText;
  
  /// Optional action button callback
  final VoidCallback? onAction;
  
  /// Optional icon to display with the action button
  final IconData? actionIcon;
  
  /// Optional padding for the section header
  final EdgeInsetsGeometry? padding;
  
  /// Whether to show a divider below the section header
  final bool showDivider;
  
  /// Optional color for the title
  final Color? titleColor;
  
  /// Optional color for the subtitle
  final Color? subtitleColor;
  
  /// Optional color for the action text
  final Color? actionTextColor;
  
  /// Optional leading icon to display before the title
  final IconData? leadingIcon;
  
  /// Optional color for the leading icon
  final Color? leadingIconColor;
  
  /// Whether to show a compact version of the section header
  final bool compact;

  /// Creates a section header with the specified parameters
  const AppSectionHeader({
    Key? key,
    required this.title,
    this.subtitle,
    this.actionText,
    this.onAction,
    this.actionIcon,
    this.padding,
    this.showDivider = false,
    this.titleColor,
    this.subtitleColor,
    this.actionTextColor,
    this.leadingIcon,
    this.leadingIconColor,
    this.compact = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: padding ?? EdgeInsets.symmetric(
            horizontal: DesignTokens.spacingLg,
            vertical: compact ? DesignTokens.spacingXs : DesignTokens.spacingMd,
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Leading icon
              if (leadingIcon != null) ...[
                Icon(
                  leadingIcon,
                  color: leadingIconColor ?? DesignTokens.colorPrimary,
                  size: compact ? DesignTokens.sizeIconSmall : DesignTokens.sizeIconMedium,
                ),
                SizedBox(width: DesignTokens.spacingMd),
              ],
              
              // Title and subtitle
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Title
                    Text(
                      title,
                      style: (compact 
                          ? TextStyleHelper.heading3(context)
                          : TextStyleHelper.heading2(context)
                      ).copyWith(
                        color: titleColor ?? DesignTokens.colorTextPrimary,
                      ),
                    ),
                    
                    // Subtitle
                    if (subtitle != null && subtitle!.isNotEmpty) ...[
                      SizedBox(height: compact ? 2.0 : DesignTokens.spacingXxs),
                      Text(
                        subtitle!,
                        style: TextStyleHelper.bodyMedium(context).copyWith(
                          color: subtitleColor ?? DesignTokens.colorTextSecondary,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              
              // Action button
              if (actionText != null && actionText!.isNotEmpty) ...[
                GestureDetector(
                  onTap: onAction,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        actionText!,
                        style: TextStyleHelper.bodyMedium(context).copyWith(
                          color: actionTextColor ?? DesignTokens.colorPrimary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (actionIcon != null) ...[
                        SizedBox(width: DesignTokens.spacingXxs),
                        Icon(
                          actionIcon,
                          color: actionTextColor ?? DesignTokens.colorPrimary,
                          size: DesignTokens.sizeIconSmall,
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
        
        // Divider
        if (showDivider)
          Divider(
            height: 1.0,
            thickness: 1.0,
            color: DesignTokens.colorDivider,
          ),
      ],
    );
  }
}
