import 'dart:io';
import 'analyze_commits.dart';
import 'update_changelog.dart';

/// Main function to automatically update version and changelog
Future<void> main(List<String> args) async {
  print('Starting automatic version update...');
  
  bool dryRun = false;
  bool forceUpdate = false;
  String forceBumpType = '';
  
  // Parse command line arguments
  for (int i = 0; i < args.length; i++) {
    if (args[i] == '--dry-run') {
      dryRun = true;
    } else if (args[i] == '--force') {
      forceUpdate = true;
      if (i + 1 < args.length && ['patch', 'minor', 'major'].contains(args[i + 1])) {
        forceBumpType = args[i + 1];
        i++;
      }
    }
  }
  
  // Analyze commits to determine bump type
  BumpType bumpType;
  
  if (forceUpdate && forceBumpType.isNotEmpty) {
    // Use forced bump type if specified
    bumpType = BumpType.values.firstWhere(
      (t) => t.name == forceBumpType,
      orElse: () => BumpType.patch,
    );
    print('Forcing version bump: ${bumpType.name}');
  } else {
    // Analyze commits to determine bump type
    bumpType = await analyzeCommits();
    print('Determined version bump: ${bumpType.name}');
  }
  
  if (bumpType == BumpType.none && !forceUpdate) {
    print('No significant changes detected. No version bump needed.');
    return;
  }
  
  // Get current version from version_info.dart
  final versionInfoFile = File('lib/core/version/version_info.dart');
  if (!versionInfoFile.existsSync()) {
    print('Error: version_info.dart file not found');
    exit(1);
  }
  
  final versionInfoContent = await versionInfoFile.readAsString();
  final versionMatch = RegExp(r'static const String version = [\'"]([^\'"]+)[\'"]').firstMatch(versionInfoContent);
  final buildNumberMatch = RegExp(r'static const int buildNumber = (\d+)').firstMatch(versionInfoContent);
  
  if (versionMatch == null || buildNumberMatch == null) {
    print('Error: Could not extract version information from version_info.dart');
    exit(1);
  }
  
  final currentVersion = versionMatch.group(1)!;
  final currentBuildNumber = int.parse(buildNumberMatch.group(1)!);
  
  print('Current version: $currentVersion (build $currentBuildNumber)');
  
  // Get commits for changelog
  final commits = await getCommitsSinceLastTag();
  
  if (dryRun) {
    print('\nDRY RUN - No changes will be made');
    print('Would update version from $currentVersion to:');
    
    // Calculate new version
    final parts = currentVersion.split('.');
    final major = int.parse(parts[0]);
    final minor = int.parse(parts[1]);
    final patch = int.parse(parts[2]);
    
    String newVersion;
    switch (bumpType) {
      case BumpType.major:
        newVersion = '${major + 1}.0.0';
        break;
      case BumpType.minor:
        newVersion = '$major.${minor + 1}.0';
        break;
      case BumpType.patch:
        newVersion = '$major.$minor.${patch + 1}';
        break;
      default:
        newVersion = currentVersion;
    }
    
    print('New version would be: $newVersion (build ${currentBuildNumber + 1})');
    
    // Print changelog preview
    print('\nChangelog entries that would be added:');
    if (commits.isEmpty) {
      print('No commits to add to changelog.');
    } else {
      for (final commit in commits) {
        print('- [${commit.type.name}] ${commit.description}${commit.isBreaking ? ' (BREAKING)' : ''}');
      }
    }
    
    return;
  }
  
  // Call the existing update_version.dart script
  final updateResult = await Process.run(
    'dart',
    ['.github/scripts/update_version.dart', bumpType.name, (currentBuildNumber + 1).toString()]
  );
  
  if (updateResult.exitCode != 0) {
    print('Error updating version: ${updateResult.stderr}');
    exit(1);
  }
  
  print(updateResult.stdout);
  
  // Read the new version
  final newVersionInfoContent = await versionInfoFile.readAsString();
  final newVersionMatch = RegExp(r'static const String version = [\'"]([^\'"]+)[\'"]').firstMatch(newVersionInfoContent);
  
  if (newVersionMatch == null) {
    print('Error: Could not extract new version information');
    exit(1);
  }
  
  final newVersion = newVersionMatch.group(1)!;
  
  // Update the changelog
  await updateChangelog(commits, newVersion);
  
  print('\nVersion updated successfully:');
  print('- Old version: $currentVersion (build $currentBuildNumber)');
  print('- New version: $newVersion (build ${currentBuildNumber + 1})');
  print('- Changelog updated with ${commits.length} commits');
  
  // Create a git tag for the new version
  print('\nTo create a git tag for this version, run:');
  print('git tag v$newVersion');
  print('git push origin v$newVersion');
}
