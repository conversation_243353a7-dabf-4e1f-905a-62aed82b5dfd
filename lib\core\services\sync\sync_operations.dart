import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as rp;

import '../../config/supabase_config.dart';
import '../../datasources/app_database.dart';
import '../../models/sync_models.dart';
import '../../providers/sync_preference_provider.dart';
import '../auth_service.dart';
import '../sync_schedule_service.dart';
import 'connectivity_monitor.dart';
import 'generic_entity_sync_handler.dart';
import 'sync_logger.dart';
import 'sync_queue.dart';

/// Enum for sync operation types
enum SyncOperation {
  upload,
  download,
  full
}

/// Provider for tracking sync state
final syncStateProvider = rp.StateProvider<rp.AsyncValue<String>>((ref) {
  return const rp.AsyncValue.data('synced');
});

/// Provider for the last sync time
final lastSyncTimeProvider = rp.StateProvider<DateTime?>((ref) => null);

/// Provider for the next scheduled sync time
final nextScheduledSyncProvider = rp.StateProvider<DateTime?>((ref) => null);

/// Class responsible for handling sync operations
class SyncOperations {
  final AppDatabase _db;
  final SupabaseConfig _supabase;
  final ConnectivityMonitor _connectivityMonitor;
  final SyncLogger _logger;
  final GenericEntitySyncHandler _entitySyncHandler;
  final SyncQueue _syncQueue;
  final rp.Ref _ref;

  /// Debounce timer for sync operations
  Timer? _syncDebounceTimer;

  /// Flag to track if sync is in progress
  bool _isSyncing = false;

  /// Threshold for number of pending changes to trigger immediate sync
  int _pendingChangesThreshold = 20;

  /// Constructor
  SyncOperations(
    this._db,
    this._supabase,
    this._logger,
    this._connectivityMonitor,
    // Removed unused parameters:
    // bool syncEnabled,
    // AuthService authService,
    this._syncQueue,
    this._ref,
  ) : _entitySyncHandler = GenericEntitySyncHandler(_supabase, _logger) {
    _initializeSyncData();
    _initializeSyncQueue();
  }

  /// Initialize the sync queue
  Future<void> _initializeSyncQueue() async {
    await _syncQueue.initialize();
    _logger.info('Sync queue initialized with ${_syncQueue.pendingOperationsCount} pending operations');
  }

  /// Initialize sync data (last sync time and next scheduled sync)
  Future<void> _initializeSyncData() async {
    await _initializeLastSyncTime();
    await _initializeNextScheduledSync();
  }

  /// Initialize the last sync time from database
  Future<void> _initializeLastSyncTime() async {
    try {
      // Get the last sync time from database
      final lastSyncTime = await _db.getLastSyncTime();

      // Update the provider with the retrieved value
      if (lastSyncTime != null) {
        _ref.read(lastSyncTimeProvider.notifier).state = lastSyncTime;
        _logger.addLogEntry('Initialized last sync time: ${lastSyncTime.toIso8601String()}');
      } else {
        _logger.addLogEntry('No previous sync time found in database');
      }
    } catch (e) {
      _logger.addLogEntry('Error initializing last sync time: $e');
    }
  }

  /// Initialize the next scheduled sync time
  Future<void> _initializeNextScheduledSync() async {
    try {
      // Get the next scheduled sync time
      final nextSync = await SyncScheduleService.getNextScheduledSync();

      // If no next sync is scheduled, schedule one
      if (nextSync == null) {
        final newNextSync = await SyncScheduleService.scheduleNextSync();
        _ref.read(nextScheduledSyncProvider.notifier).state = newNextSync;
        _logger.addLogEntry('Scheduled next sync for ${newNextSync.toIso8601String()}');
      } else {
        _ref.read(nextScheduledSyncProvider.notifier).state = nextSync;
        _logger.addLogEntry('Next sync scheduled for ${nextSync.toIso8601String()}');
      }

      // Get the pending changes threshold
      _pendingChangesThreshold = await SyncScheduleService.getPendingChangesThreshold();
    } catch (e) {
      _logger.addLogEntry('Error initializing next scheduled sync: $e');
    }
  }

  /// Get the total number of pending changes
  Future<int> _getTotalPendingChangesCount() async {
    try {
      // Get all unsynchronized records from each table
      final incomeRecords = await _db.getUnsyncedIncome();
      final orderRecords = await _db.getUnsyncedOrders();
      final performanceRecords = await _db.getUnsyncedPerformance();
      final sparePartRecords = await _db.getUnsyncedSpareParts();
      final sparePartHistoryRecords = await _db.getUnsyncedSparePartsHistory();

      // Calculate total
      final totalCount = incomeRecords.length + orderRecords.length +
                         performanceRecords.length + sparePartRecords.length +
                         sparePartHistoryRecords.length;

      return totalCount;
    } catch (e) {
      _logger.addLogEntry('Error getting pending changes count: $e');
      return 0;
    }
  }

  /// Check if a sync should be performed based on schedule or pending changes
  Future<bool> _shouldPerformSync(SyncOperation operation) async {
    // For manual sync, always return true
    if (operation == SyncOperation.upload) {
      // For upload operations, check if there are any pending changes
      final pendingChanges = await _getTotalPendingChangesCount();
      return pendingChanges > 0;
    }

    // For full sync, check schedule and pending changes
    if (operation == SyncOperation.full) {
      // Check if there are enough pending changes to trigger immediate sync
      final pendingChanges = await _getTotalPendingChangesCount();
      if (pendingChanges >= _pendingChangesThreshold) {
        _logger.addLogEntry('Significant number of pending changes ($pendingChanges) detected, triggering immediate sync');
        return true;
      }

      // Check if it's time for a scheduled sync
      final isScheduledSyncDue = await SyncScheduleService.isScheduledSyncDue();
      if (isScheduledSyncDue) {
        _logger.addLogEntry('Scheduled sync is due, triggering sync');
        return true;
      }

      // Not time for sync yet
      final nextSync = await SyncScheduleService.getNextScheduledSync();
      if (nextSync != null) {
        _logger.addLogEntry('Scheduled sync not due yet, next sync at ${nextSync.toIso8601String()}');
      }
      return false;
    }

    // For download operations, always return true
    return true;
  }

  /// Main sync method with debounce
  Future<void> syncData(SyncOperation operation, {Duration debounceTime = const Duration(seconds: 5)}) async {
    // Check if auto sync is enabled
    final syncEnabled = _ref.read(syncEnabledProvider);
    if (!syncEnabled) {
      _logger.addLogEntry('Auto sync is disabled, skipping automatic sync operation: $operation');
      return;
    }

    // Cancel any pending sync operation
    _syncDebounceTimer?.cancel();

    // Start a new debounce timer
    _syncDebounceTimer = Timer(debounceTime, () async {
      try {
        // Check if sync should be performed based on schedule or pending changes
        final shouldSync = await _shouldPerformSync(operation);
        if (!shouldSync) {
          _logger.addLogEntry('Skipping scheduled sync operation: $operation');
          return;
        }

        await _executeSyncOperation(operation);
      } catch (e) {
        debugPrint('Sync error: $e');
      }
    });
  }

  /// Execute sync operation without debouncing
  Future<void> syncNow(SyncOperation operation, {bool isManualSync = false}) async {
    try {
      // If it's not explicitly marked as a manual sync, try to detect if it's from the sync screen
      if (!isManualSync) {
        try {
          // Get the current stack trace to check if this is called from the sync screen
          final stackTrace = StackTrace.current.toString();
          isManualSync = stackTrace.contains('sync_screen.dart');
        } catch (e) {
          // If we can't determine, assume it's not a manual sync
          isManualSync = false;
        }
      }

      // If it's not a manual sync, check if auto sync is enabled
      if (!isManualSync) {
        final syncEnabled = _ref.read(syncEnabledProvider);
        if (!syncEnabled) {
          _logger.addLogEntry('Auto sync is disabled, skipping sync operation: $operation');
          return;
        }
      } else {
        _logger.addLogEntry('Manual sync triggered: $operation');
      }

      await _executeSyncOperation(operation);
    } catch (e) {
      debugPrint('Immediate sync error: $e');
      rethrow;
    }
  }

  /// Execute the sync operation
  Future<void> _executeSyncOperation(SyncOperation operation) async {
    // Don't run multiple syncs at the same time
    if (_isSyncing) {
      _logger.addLogEntry('Sync already in progress, skipping operation: $operation');
      return;
    }

    _isSyncing = true;
    _logger.addLogEntry('Starting sync operation: $operation');

    // Update UI state to show syncing
    _ref.read(syncStateProvider.notifier).state = const rp.AsyncValue.loading();

    try {
      // Check connectivity and authentication
      if (!await _checkConnectivityAndAuth()) {
        return;
      }

      bool syncSuccessful = true;
      const String errorMessage = '';

      // Execute the appropriate sync operation
      switch (operation) {
        case SyncOperation.upload:
          syncSuccessful = await _handleUploadOperation(errorMessage);
          break;
        case SyncOperation.download:
          syncSuccessful = await _handleDownloadOperation(errorMessage);
          break;
        case SyncOperation.full:
          syncSuccessful = await _handleFullSyncOperation(errorMessage);
          break;
      }

      // Update last sync time and UI state
      await _updateLastSyncTimeAndState(syncSuccessful, errorMessage);

    } catch (e) {
      _logger.addLogEntry('Sync failed with error: $e');

      // Update UI state to show error
      _ref.read(syncStateProvider.notifier).state =
          rp.AsyncValue.error('Sync failed: $e', StackTrace.current);
      // Don't rethrow, so the app can continue
    } finally {
      _isSyncing = false;
    }
  }

  /// Check connectivity and authentication
  Future<bool> _checkConnectivityAndAuth() async {
    // Check internet connection
    final connected = await _connectivityMonitor.isConnected();
    if (!connected) {
      _logger.addLogEntry('No internet connection, skipping sync');

      // Update UI state to show error
      _ref.read(syncStateProvider.notifier).state =
          rp.AsyncValue.error('No internet connection', StackTrace.current);

      return false;
    }

    // Ensure Supabase is initialized
    if (!_supabase.isInitialized) {
      _logger.addLogEntry('Initializing Supabase connection');
      try {
        await _supabase.initialize();
      } catch (e) {
        _logger.addLogEntry('Error initializing Supabase: $e');
        _ref.read(syncStateProvider.notifier).state =
            rp.AsyncValue.error('Error initializing Supabase: $e', StackTrace.current);
        return false;
      }
    }

    // Check if user is authenticated
    final authService = _ref.read(authServiceProvider);
    if (!authService.isAuthenticated) {
      _logger.addLogEntry('User not authenticated, skipping sync. Authentication is optional but required for sync.');

      // Update UI state to show error with more helpful message
      _ref.read(syncStateProvider.notifier).state =
          rp.AsyncValue.error('Login required for sync. Go to More > Login to enable sync.', StackTrace.current);

      return false;
    }

    return true;
  }

  /// Handle upload operation
  Future<bool> _handleUploadOperation(String errorMessage) async {
    _logger.addLogEntry('Starting upload operation');
    try {
      await _uploadPendingChanges();
      return true;
    } catch (e) {
      errorMessage = 'Upload failed: $e';
      _logger.addLogEntry(errorMessage);
      return false;
    }
  }

  /// Handle download operation
  Future<bool> _handleDownloadOperation(String errorMessage) async {
    _logger.addLogEntry('Starting download operation');
    try {
      await _downloadRecentChanges();
      return true;
    } catch (e) {
      errorMessage = 'Download failed: $e';
      _logger.addLogEntry(errorMessage);
      return false;
    }
  }

  /// Handle full sync operation (upload + download)
  Future<bool> _handleFullSyncOperation(String errorMessage) async {
    _logger.addLogEntry('Starting full sync operation');
    bool uploadSuccessful = true;
    bool downloadSuccessful = true;

    try {
      await _uploadPendingChanges();
    } catch (e) {
      uploadSuccessful = false;
      errorMessage = 'Upload part of full sync failed: $e';
      _logger.addLogEntry(errorMessage);
      // Still try to download even if upload fails
    }

    try {
      await _downloadRecentChanges();
    } catch (e) {
      downloadSuccessful = false;
      errorMessage = errorMessage.isEmpty
          ? 'Download part of full sync failed: $e'
          : '$errorMessage, Download part of full sync failed: $e';
      _logger.addLogEntry('Download part of full sync failed: $e');
    }

    return uploadSuccessful && downloadSuccessful;
  }

  /// Update last sync time and UI state
  Future<void> _updateLastSyncTimeAndState(bool syncSuccessful, String errorMessage) async {
    try {
      final now = DateTime.now().toUtc();
      await _db.updateLastSyncTime(now);

      if (syncSuccessful) {
        _logger.addLogEntry('Sync completed successfully');

        // Update UI state to show success
        _ref.read(syncStateProvider.notifier).state =
            const rp.AsyncValue.data('synced');

        // Schedule next sync
        final nextSync = await SyncScheduleService.scheduleNextSync();
        _ref.read(nextScheduledSyncProvider.notifier).state = nextSync;
        _logger.addLogEntry('Next sync scheduled for ${nextSync.toIso8601String()}');
      } else {
        _logger.addLogEntry('Sync completed with some errors: $errorMessage');

        // Still update UI state to show partial success
        _ref.read(syncStateProvider.notifier).state =
            const rp.AsyncValue.data('synced with errors');

        // Even with errors, schedule next sync
        final nextSync = await SyncScheduleService.scheduleNextSync();
        _ref.read(nextScheduledSyncProvider.notifier).state = nextSync;
        _logger.addLogEntry('Next sync scheduled for ${nextSync.toIso8601String()} (after sync with errors)');
      }

      // Always update last sync time provider
      _ref.read(lastSyncTimeProvider.notifier).state = now;
    } catch (e) {
      _logger.addLogEntry('Error updating last sync time: $e');

      // Update UI state to show error
      if (!syncSuccessful) {
        _ref.read(syncStateProvider.notifier).state =
            rp.AsyncValue.error(errorMessage, StackTrace.current);
      }
    }
  }

  /// Upload pending changes to Supabase
  Future<void> _uploadPendingChanges() async {
    try {
      _logger.addLogEntry('Fetching unsynced records from local database');

      // Get all unsynchronized records from each table
      final incomeRecords = await _db.getUnsyncedIncome();
      final orderRecords = await _db.getUnsyncedOrders();
      final performanceRecords = await _db.getUnsyncedPerformance();
      final sparePartRecords = await _db.getUnsyncedSpareParts();
      final sparePartHistoryRecords = await _db.getUnsyncedSparePartsHistory();

      // Check if there are any records to upload
      final hasUnsyncedRecords = incomeRecords.isNotEmpty ||
        orderRecords.isNotEmpty ||
        performanceRecords.isNotEmpty ||
        sparePartRecords.isNotEmpty ||
        sparePartHistoryRecords.isNotEmpty;

      if (!hasUnsyncedRecords) {
        _logger.addLogEntry('No unsynced records to upload');
        return;
      }

      final int totalRecords = incomeRecords.length + orderRecords.length +
                         performanceRecords.length + sparePartRecords.length +
                         sparePartHistoryRecords.length;
      _logger.addLogEntry('Found $totalRecords records to upload');

      // Upload each type of record using the generic handler
      await _uploadIncomeRecords(incomeRecords);
      await _uploadOrderRecords(orderRecords);
      await _uploadPerformanceRecords(performanceRecords);
      await _uploadSparePartRecords(sparePartRecords);
      await _uploadSparePartHistoryRecords(sparePartHistoryRecords);

      _logger.addLogEntry('Upload operation completed successfully');

    } catch (e) {
      _logger.addLogEntry('Error uploading pending changes: $e');
      debugPrint('Error uploading pending changes: $e');
      rethrow;
    }
  }

  /// Upload income records
  Future<void> _uploadIncomeRecords(List<IncomeData> records) async {
    await _entitySyncHandler.uploadEntityRecords<IncomeData, IncomeSyncModel>(
      tableName: 'income',
      records: records,
      toSyncModel: (record) => IncomeSyncModel.fromDrift(record),
      markAsSynced: _db.markIncomeAsSynced,
      getUuid: (record) => record.uuid,
      getDeletedAt: (record) => record.deletedAt,
    );
  }

  /// Upload order records
  Future<void> _uploadOrderRecords(List<Order> records) async {
    await _entitySyncHandler.uploadEntityRecords<Order, OrdersSyncModel>(
      tableName: 'orders',
      records: records,
      toSyncModel: (record) => OrdersSyncModel.fromDrift(record),
      markAsSynced: _db.markOrderAsSynced,
      getUuid: (record) => record.uuid,
      getDeletedAt: (record) => record.deletedAt,
    );
  }

  /// Upload performance records
  Future<void> _uploadPerformanceRecords(List<PerformanceData> records) async {
    await _entitySyncHandler.uploadEntityRecords<PerformanceData, PerformanceSyncModel>(
      tableName: 'performance',
      records: records,
      toSyncModel: (record) => PerformanceSyncModel.fromDrift(record),
      markAsSynced: _db.markPerformanceAsSynced,
      getUuid: (record) => record.uuid,
      getDeletedAt: (record) => record.deletedAt,
    );
  }

  /// Upload spare part records
  Future<void> _uploadSparePartRecords(List<SparePart> records) async {
    await _entitySyncHandler.uploadEntityRecords<SparePart, SparePartsSyncModel>(
      tableName: 'spare_parts',
      records: records,
      toSyncModel: (record) => SparePartsSyncModel.fromDrift(record),
      markAsSynced: _db.markSparePartAsSynced,
      getUuid: (record) => record.uuid,
      getDeletedAt: (record) => record.deletedAt,
    );
  }

  /// Upload spare part history records
  Future<void> _uploadSparePartHistoryRecords(List<SparePartsHistoryData> records) async {
    await _entitySyncHandler.uploadEntityRecords<SparePartsHistoryData, SparePartsHistorySyncModel>(
      tableName: 'spare_parts_history',
      records: records,
      toSyncModel: (record) => SparePartsHistorySyncModel.fromDrift(record),
      markAsSynced: _db.markSparePartHistoryAsSynced,
      getUuid: (record) => record.uuid,
      getDeletedAt: (record) => record.deletedAt,
    );
  }

  /// Download recent changes from Supabase
  Future<void> _downloadRecentChanges() async {
    try {
      _logger.addLogEntry('Starting download of recent changes');

      // Get the last sync time
      final lastSyncTime = await _db.getLastSyncTime();

      if (lastSyncTime == null) {
        _logger.addLogEntry('No previous sync found, performing full download');
        // If this is the first sync, fetch all records
        await _fetchAllRecords();
        return;
      }

      // Log the raw lastSyncTime for debugging
      _logger.addLogEntry('Raw lastSyncTime: $lastSyncTime');

      // Add a small buffer (1 second) to avoid precision issues
      // Ensure the time is in UTC and handle potential format issues
      DateTime syncTimeCutoff;
      try {
        syncTimeCutoff = lastSyncTime.toUtc().subtract(const Duration(seconds: 1));
      } catch (e) {
        // If there's an error, use current time minus 1 day as fallback
        _logger.addLogEntry('Error processing lastSyncTime: $e, using fallback');
        syncTimeCutoff = DateTime.now().toUtc().subtract(const Duration(days: 1));
      }

      final syncTimeStr = syncTimeCutoff.toIso8601String();

      _logger.addLogEntry('Fetching records updated after $syncTimeStr');

      // Fetch records that were updated after the last sync time
      await _fetchRecentChanges(syncTimeStr);

      _logger.addLogEntry('Download operation completed successfully');

    } catch (e) {
      _logger.addLogEntry('Error downloading recent changes: $e');
      debugPrint('Error downloading recent changes: $e');
      rethrow;
    }
  }

  /// Fetch all records from Supabase (for first sync)
  Future<void> _fetchAllRecords() async {
    try {
      _logger.addLogEntry('Fetching all records from Supabase (first sync)');

      // Fetch all income records
      _logger.addLogEntry('Fetching income records');
      final incomeResponse = await _supabase.client
        .from('income')
        .select();
      _logger.addLogEntry('Fetched ${incomeResponse.length} income records');

      _logger.addLogEntry('Fetching order records');
      final orderResponse = await _supabase.client
        .from('orders')
        .select();
      _logger.addLogEntry('Fetched ${orderResponse.length} order records');

      _logger.addLogEntry('Fetching performance records');
      final performanceResponse = await _supabase.client
        .from('performance')
        .select();
      _logger.addLogEntry('Fetched ${performanceResponse.length} performance records');

      _logger.addLogEntry('Fetching spare parts records');
      final sparePartsResponse = await _supabase.client
        .from('spare_parts')
        .select();
      _logger.addLogEntry('Fetched ${sparePartsResponse.length} spare parts records');

      _logger.addLogEntry('Fetching spare parts history records');
      final sparePartsHistoryResponse = await _supabase.client
        .from('spare_parts_history')
        .select();
      _logger.addLogEntry('Fetched ${sparePartsHistoryResponse.length} spare parts history records');

      final int totalRecords = incomeResponse.length + orderResponse.length +
                         performanceResponse.length + sparePartsResponse.length +
                         sparePartsHistoryResponse.length;
      _logger.addLogEntry('Processing $totalRecords records from cloud');

      // Process each type of record
      await _processIncomeRecords(incomeResponse);
      await _processOrderRecords(orderResponse);
      await _processPerformanceRecords(performanceResponse);
      await _processSparePartRecords(sparePartsResponse);
      await _processSparePartHistoryRecords(sparePartsHistoryResponse);

      _logger.addLogEntry('Completed processing all records from cloud');

    } catch (e) {
      _logger.addLogEntry('Error fetching all records: $e');
      debugPrint('Error fetching all records: $e');
      rethrow;
    }
  }

  /// Fetch only recent changes from Supabase
  Future<void> _fetchRecentChanges(String syncTimeStr) async {
    try {
      _logger.addLogEntry('Fetching recent changes since $syncTimeStr');

      List<dynamic> incomeResponse = [];
      List<dynamic> orderResponse = [];
      List<dynamic> performanceResponse = [];
      List<dynamic> sparePartsResponse = [];
      List<dynamic> sparePartsHistoryResponse = [];

      // Use try-catch blocks for each fetch to handle errors gracefully
      try {
        // Fetch income records updated after last sync
        _logger.addLogEntry('Fetching recent income records');
        incomeResponse = await _supabase.client
          .from('income')
          .select()
          .gt('updated_at', syncTimeStr);
        _logger.addLogEntry('Fetched ${incomeResponse.length} recent income records');
      } catch (e) {
        _logger.addLogEntry('Error fetching income records: $e');
        debugPrint('Error fetching income records: $e');
        // Continue with other fetches
      }

      try {
        // Fetch order records updated after last sync
        _logger.addLogEntry('Fetching recent order records');
        orderResponse = await _supabase.client
          .from('orders')
          .select()
          .gt('updated_at', syncTimeStr);
        _logger.addLogEntry('Fetched ${orderResponse.length} recent order records');
      } catch (e) {
        _logger.addLogEntry('Error fetching order records: $e');
        debugPrint('Error fetching order records: $e');
        // Continue with other fetches
      }

      try {
        // Fetch performance records updated after last sync
        _logger.addLogEntry('Fetching recent performance records');
        performanceResponse = await _supabase.client
          .from('performance')
          .select()
          .gt('updated_at', syncTimeStr);
        _logger.addLogEntry('Fetched ${performanceResponse.length} recent performance records');
      } catch (e) {
        _logger.addLogEntry('Error fetching performance records: $e');
        debugPrint('Error fetching performance records: $e');
        // Continue with other fetches
      }

      try {
        // Fetch spare parts records updated after last sync
        _logger.addLogEntry('Fetching recent spare parts records');
        sparePartsResponse = await _supabase.client
          .from('spare_parts')
          .select()
          .gt('updated_at', syncTimeStr);
        _logger.addLogEntry('Fetched ${sparePartsResponse.length} recent spare parts records');
      } catch (e) {
        _logger.addLogEntry('Error fetching spare parts records: $e');
        debugPrint('Error fetching spare parts records: $e');
        // Continue with other fetches
      }

      try {
        // Fetch spare parts history records updated after last sync
        _logger.addLogEntry('Fetching recent spare parts history records');
        sparePartsHistoryResponse = await _supabase.client
          .from('spare_parts_history')
          .select()
          .gt('updated_at', syncTimeStr);
        _logger.addLogEntry('Fetched ${sparePartsHistoryResponse.length} recent spare parts history records');
      } catch (e) {
        _logger.addLogEntry('Error fetching spare parts history records: $e');
        debugPrint('Error fetching spare parts history records: $e');
        // Continue with other fetches
      }

      final int totalRecords = incomeResponse.length + orderResponse.length +
                         performanceResponse.length + sparePartsResponse.length +
                         sparePartsHistoryResponse.length;

      if (totalRecords == 0) {
        _logger.addLogEntry('No recent changes found');
        return;
      }

      _logger.addLogEntry('Processing $totalRecords recent records from cloud');

      // Process each type of record
      await _processIncomeRecords(incomeResponse);
      await _processOrderRecords(orderResponse);
      await _processPerformanceRecords(performanceResponse);
      await _processSparePartRecords(sparePartsResponse);
      await _processSparePartHistoryRecords(sparePartsHistoryResponse);

      _logger.addLogEntry('Completed processing recent changes');

    } catch (e) {
      _logger.addLogEntry('Error fetching recent changes: $e');
      debugPrint('Error fetching recent changes: $e');
      // Don't rethrow, so the sync process can continue
    }
  }

  /// Process income records
  Future<void> _processIncomeRecords(List<dynamic> records) async {
    if (records.isEmpty) {
      return;
    }

    _logger.addLogEntry('Processing ${records.length} income records');
    for (final record in records) {
      try {
        await _entitySyncHandler.insertOrUpdateRecord<IncomeData, IncomeCompanion>(
          jsonData: record,
          entityName: 'income',
          uuid: record['uuid'],
          getExistingRecords: (uuid) => (_db.select(_db.income)..where((t) => t.uuid.equals(uuid))).get(),
          fromJson: IncomeSyncModel.fromJson,
          insertRecord: (data) => _db.into(_db.income).insert(data),
          updateRecord: (uuid, data) => (_db.update(_db.income)..where((t) => t.uuid.equals(uuid))).write(data),
          getUpdatedAt: (record) => record.updatedAt,
          getSyncStatus: (record) => record.syncStatus.toString().split('.').last,
        );
      } catch (e) {
        _logger.addLogEntry('Error processing income record: ${record['uuid']} - $e');
        // Continue with other records
      }
    }
  }

  /// Process order records
  Future<void> _processOrderRecords(List<dynamic> records) async {
    if (records.isEmpty) {
      return;
    }

    _logger.addLogEntry('Processing ${records.length} order records');
    for (final record in records) {
      try {
        await _entitySyncHandler.insertOrUpdateRecord<Order, OrdersCompanion>(
          jsonData: record,
          entityName: 'order',
          uuid: record['uuid'],
          getExistingRecords: (uuid) => (_db.select(_db.orders)..where((t) => t.uuid.equals(uuid))).get(),
          fromJson: OrdersSyncModel.fromJson,
          insertRecord: (data) => _db.into(_db.orders).insert(data),
          updateRecord: (uuid, data) => (_db.update(_db.orders)..where((t) => t.uuid.equals(uuid))).write(data),
          getUpdatedAt: (record) => record.updatedAt,
          getSyncStatus: (record) => record.syncStatus.toString().split('.').last,
        );
      } catch (e) {
        _logger.addLogEntry('Error processing order record: ${record['uuid']} - $e');
        // Continue with other records
      }
    }
  }

  /// Process performance records
  Future<void> _processPerformanceRecords(List<dynamic> records) async {
    if (records.isEmpty) {
      return;
    }

    _logger.addLogEntry('Processing ${records.length} performance records');
    for (final record in records) {
      try {
        await _entitySyncHandler.insertOrUpdateRecord<PerformanceData, PerformanceCompanion>(
          jsonData: record,
          entityName: 'performance',
          uuid: record['uuid'],
          getExistingRecords: (uuid) => (_db.select(_db.performance)..where((t) => t.uuid.equals(uuid))).get(),
          fromJson: PerformanceSyncModel.fromJson,
          insertRecord: (data) => _db.into(_db.performance).insert(data),
          updateRecord: (uuid, data) => (_db.update(_db.performance)..where((t) => t.uuid.equals(uuid))).write(data),
          getUpdatedAt: (record) => record.updatedAt,
          getSyncStatus: (record) => record.syncStatus.toString().split('.').last,
        );
      } catch (e) {
        _logger.addLogEntry('Error processing performance record: ${record['uuid']} - $e');
        // Continue with other records
      }
    }
  }

  /// Process spare part records
  Future<void> _processSparePartRecords(List<dynamic> records) async {
    if (records.isEmpty) {
      return;
    }

    _logger.addLogEntry('Processing ${records.length} spare part records');
    for (final record in records) {
      try {
        await _entitySyncHandler.insertOrUpdateRecord<SparePart, SparePartsCompanion>(
          jsonData: record,
          entityName: 'spare part',
          uuid: record['uuid'],
          getExistingRecords: (uuid) => (_db.select(_db.spareParts)..where((t) => t.uuid.equals(uuid))).get(),
          fromJson: SparePartsSyncModel.fromJson,
          insertRecord: (data) => _db.into(_db.spareParts).insert(data),
          updateRecord: (uuid, data) => (_db.update(_db.spareParts)..where((t) => t.uuid.equals(uuid))).write(data),
          getUpdatedAt: (record) => record.updatedAt,
          getSyncStatus: (record) => record.syncStatus.toString().split('.').last,
        );
      } catch (e) {
        _logger.addLogEntry('Error processing spare part record: ${record['uuid']} - $e');
        // Continue with other records
      }
    }
  }

  /// Process spare part history records
  Future<void> _processSparePartHistoryRecords(List<dynamic> records) async {
    if (records.isEmpty) {
      return;
    }

    _logger.addLogEntry('Processing ${records.length} spare part history records');
    for (final record in records) {
      try {
        await _entitySyncHandler.insertOrUpdateRecord<SparePartsHistoryData, SparePartsHistoryCompanion>(
          jsonData: record,
          entityName: 'spare part history',
          uuid: record['uuid'],
          getExistingRecords: (uuid) => (_db.select(_db.sparePartsHistory)..where((t) => t.uuid.equals(uuid))).get(),
          fromJson: SparePartsHistorySyncModel.fromJson,
          insertRecord: (data) => _db.into(_db.sparePartsHistory).insert(data),
          updateRecord: (uuid, data) => (_db.update(_db.sparePartsHistory)..where((t) => t.uuid.equals(uuid))).write(data),
          getUpdatedAt: (record) => record.updatedAt,
          getSyncStatus: (record) => record.syncStatus.toString().split('.').last,
        );
      } catch (e) {
        _logger.addLogEntry('Error processing spare part history record: ${record['uuid']} - $e');
        // Continue with other records
      }
    }
  }

  /// Dispose resources
  void dispose() {
    _syncDebounceTimer?.cancel();
  }
}
