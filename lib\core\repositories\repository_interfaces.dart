import 'package:dartz/dartz.dart';
import '../errors/failures.dart';

/// Generic repository interface for CRUD operations
///
/// This interface defines the common CRUD operations that all repositories should implement.
/// It is parameterized with the entity type [T] and the ID type [ID].
abstract class IRepository<T, ID> {
  /// Get all entities
  Future<Either<Failure, List<T>>> getAll();
  
  /// Get an entity by its ID
  Future<Either<Failure, T>> getById(ID id);
  
  /// Save a new entity
  Future<Either<Failure, T>> save(T entity);
  
  /// Update an existing entity
  Future<Either<Failure, T>> update(T entity);
  
  /// Delete an entity by its ID
  Future<Either<Failure, bool>> delete(ID id);
}

/// Interface for repositories that support date range filtering
///
/// This interface extends the base repository interface with methods for
/// filtering entities by date range.
abstract class IDateRangeRepository<T, ID> extends IRepository<T, ID> {
  /// Get entities for a specific date range
  Future<Either<Failure, List<T>>> getForDateRange(DateTime start, DateTime end);
  
  /// Check if an entity already exists for a specific date
  Future<Either<Failure, bool>> checkDateExists(DateTime date, {ID? excludeId});
}

/// Interface for repositories that support synchronization
///
/// This interface extends the base repository interface with methods for
/// synchronizing entities with a remote data source.
abstract class ISyncableRepository<T, ID> extends IRepository<T, ID> {
  /// Get entities that need to be synchronized
  Future<Either<Failure, List<T>>> getUnsyncedEntities();
  
  /// Mark an entity as synchronized
  Future<Either<Failure, bool>> markAsSynced(String uuid);
  
  /// Trigger synchronization for this entity type
  Future<Either<Failure, bool>> syncEntities();
}

/// Interface for repositories that support soft deletion
///
/// This interface extends the base repository interface with methods for
/// soft deleting entities (marking them as deleted without removing them).
abstract class ISoftDeleteRepository<T, ID> extends IRepository<T, ID> {
  /// Soft delete an entity by its ID
  Future<Either<Failure, bool>> softDelete(ID id);
  
  /// Get all entities including soft-deleted ones
  Future<Either<Failure, List<T>>> getAllIncludingDeleted();
  
  /// Restore a soft-deleted entity
  Future<Either<Failure, bool>> restore(ID id);
}
