import 'package:flutter/material.dart';
import '../../theme/design_tokens.dart';
import '../../utils/text_style_helper.dart';

/// A reusable list item component that follows the design system
///
/// This list item adapts its appearance based on the specified parameters,
/// providing a consistent way to display list items across the application.
class AppListItem extends StatelessWidget {
  /// The title to display in the list item
  final String title;

  /// Optional subtitle to display in the list item
  final String? subtitle;

  /// Optional leading widget to display in the list item
  final Widget? leading;

  /// Optional trailing widget to display in the list item
  final Widget? trailing;

  /// Optional icon to display as the leading widget
  final IconData? leadingIcon;

  /// Optional color for the leading icon
  final Color? leadingIconColor;

  /// Optional background color for the leading icon
  final Color? leadingIconBackgroundColor;

  /// Optional icon to display as the trailing widget
  final IconData? trailingIcon;

  /// Optional color for the trailing icon
  final Color? trailingIconColor;

  /// Optional callback when the list item is tapped
  final VoidCallback? onTap;

  /// Optional callback when the list item is long-pressed
  final VoidCallback? onLongPress;

  /// Optional padding for the list item
  final EdgeInsetsGeometry? padding;

  /// Whether to show a divider below the list item
  final bool showDivider;

  /// Whether to show a compact version of the list item
  final bool compact;

  /// Optional badge text to display in the list item
  final String? badgeText;

  /// Optional badge color
  final Color? badgeColor;

  /// Optional badge text color
  final Color? badgeTextColor;

  /// Optional additional information to display below the subtitle
  final String? additionalInfo;

  /// Optional border radius for the list item
  final BorderRadius? borderRadius;

  /// Optional background color for the list item
  final Color? backgroundColor;

  /// Creates a list item with the specified parameters
  const AppListItem({
    Key? key,
    required this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.leadingIcon,
    this.leadingIconColor,
    this.leadingIconBackgroundColor,
    this.trailingIcon,
    this.trailingIconColor,
    this.onTap,
    this.onLongPress,
    this.padding,
    this.showDivider = false,
    this.compact = false,
    this.badgeText,
    this.badgeColor,
    this.badgeTextColor,
    this.additionalInfo,
    this.borderRadius,
    this.backgroundColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Create leading widget if needed
    Widget? leadingWidget = leading;
    if (leadingWidget == null && leadingIcon != null) {
      leadingWidget = Container(
        width: compact ? 32.0 : 40.0,
        height: compact ? 32.0 : 40.0,
        decoration: BoxDecoration(
          color: leadingIconBackgroundColor ?? DesignTokens.colorPrimaryLight.withAlpha(51),
          borderRadius: BorderRadius.circular(compact ? 8.0 : 10.0),
        ),
        child: Icon(
          leadingIcon,
          color: leadingIconColor ?? DesignTokens.colorPrimary,
          size: compact ? DesignTokens.sizeIconSmall : DesignTokens.sizeIconMedium,
        ),
      );
    }

    // Create trailing widget if needed
    Widget? trailingWidget = trailing;
    if (trailingWidget == null && trailingIcon != null) {
      trailingWidget = Icon(
        trailingIcon,
        color: trailingIconColor ?? DesignTokens.colorTextSecondary,
        size: compact ? DesignTokens.sizeIconSmall : DesignTokens.sizeIconMedium,
      );
    }

    // Create badge widget if needed
    Widget? badgeWidget;
    if (badgeText != null && badgeText!.isNotEmpty) {
      badgeWidget = Container(
        padding: EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingXs,
          vertical: 2.0,
        ),
        decoration: BoxDecoration(
          color: badgeColor ?? DesignTokens.colorPrimary,
          borderRadius: BorderRadius.circular(4.0),
        ),
        child: Text(
          badgeText!,
          style: TextStyleHelper.bodySmall(context).copyWith(
            color: badgeTextColor ?? DesignTokens.colorTextLight,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }

    // Create list item content
    Widget content = Padding(
      padding: padding ?? (compact
          ? EdgeInsets.symmetric(
              horizontal: DesignTokens.spacingMd,
              vertical: DesignTokens.spacingXs,
            )
          : DesignTokens.paddingListItem),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Leading widget
          if (leadingWidget != null) ...[
            leadingWidget,
            SizedBox(width: DesignTokens.spacingMd),
          ],

          // Title, subtitle, and additional info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Title and badge
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: compact
                            ? TextStyleHelper.bodyMedium(context).copyWith(fontWeight: FontWeight.w500)
                            : TextStyleHelper.bodyLarge(context).copyWith(fontWeight: FontWeight.w500),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (badgeWidget != null) ...[
                      SizedBox(width: DesignTokens.spacingXs),
                      badgeWidget,
                    ],
                  ],
                ),

                // Subtitle
                if (subtitle != null && subtitle!.isNotEmpty) ...[
                  SizedBox(height: compact ? 2.0 : DesignTokens.spacingXxs),
                  Text(
                    subtitle!,
                    style: compact
                        ? TextStyleHelper.bodySmall(context)
                        : TextStyleHelper.bodyMedium(context),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],

                // Additional info
                if (additionalInfo != null && additionalInfo!.isNotEmpty) ...[
                  SizedBox(height: compact ? 2.0 : DesignTokens.spacingXxs),
                  Text(
                    additionalInfo!,
                    style: TextStyleHelper.bodySmall(context).copyWith(
                      color: DesignTokens.colorTextSecondary,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),

          // Trailing widget
          if (trailingWidget != null) ...[
            SizedBox(width: DesignTokens.spacingMd),
            trailingWidget,
          ],
        ],
      ),
    );

    // Add tap behavior if needed
    if (onTap != null || onLongPress != null) {
      content = InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        borderRadius: borderRadius,
        child: content,
      );
    }

    // Add background color if needed
    if (backgroundColor != null || borderRadius != null) {
      content = Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: borderRadius,
        ),
        child: content,
      );
    }

    // Add divider if needed
    if (showDivider) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          content,
          Divider(
            height: 1.0,
            thickness: 1.0,
            color: DesignTokens.colorDivider,
            indent: leadingWidget != null ? (compact ? 72.0 : 80.0) : 0.0,
          ),
        ],
      );
    }

    return content;
  }
}
