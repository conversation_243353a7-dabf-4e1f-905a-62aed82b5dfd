import 'package:flutter/material.dart';
import '../../theme/design_tokens.dart';
import '../../utils/text_style_helper.dart';
import '../buttons/app_button.dart';

/// A reusable empty state component that follows the design system
///
/// This empty state adapts its appearance based on the specified parameters,
/// providing a consistent way to display empty states across the application.
class AppEmptyState extends StatelessWidget {
  /// The title to display in the empty state
  final String title;
  
  /// Optional message to display in the empty state
  final String? message;
  
  /// Optional icon to display in the empty state
  final IconData icon;
  
  /// Optional image asset to display in the empty state
  final String? imageAsset;
  
  /// Optional action button text
  final String? actionButtonText;
  
  /// Optional action button callback
  final VoidCallback? onActionButtonPressed;
  
  /// Optional secondary action button text
  final String? secondaryActionButtonText;
  
  /// Optional secondary action button callback
  final VoidCallback? onSecondaryActionButtonPressed;
  
  /// Optional color for the icon
  final Color? iconColor;
  
  /// Optional size for the icon
  final double? iconSize;
  
  /// Optional padding for the empty state
  final EdgeInsetsGeometry? padding;
  
  /// Whether to show a compact version of the empty state
  final bool compact;

  /// Creates an empty state with the specified parameters
  const AppEmptyState({
    Key? key,
    required this.title,
    this.message,
    this.icon = Icons.inbox,
    this.imageAsset,
    this.actionButtonText,
    this.onActionButtonPressed,
    this.secondaryActionButtonText,
    this.onSecondaryActionButtonPressed,
    this.iconColor,
    this.iconSize,
    this.padding,
    this.compact = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? EdgeInsets.all(DesignTokens.spacingLg),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Image or icon
          if (imageAsset != null) ...[
            Image.asset(
              imageAsset!,
              width: compact ? 80.0 : 120.0,
              height: compact ? 80.0 : 120.0,
            ),
          ] else ...[
            Icon(
              icon,
              size: iconSize ?? (compact ? 48.0 : 64.0),
              color: iconColor ?? DesignTokens.colorTextSecondary,
            ),
          ],
          
          // Spacing
          SizedBox(height: compact ? DesignTokens.spacingMd : DesignTokens.spacingLg),
          
          // Title
          Text(
            title,
            style: compact 
                ? TextStyleHelper.heading3(context)
                : TextStyleHelper.heading2(context),
            textAlign: TextAlign.center,
          ),
          
          // Message
          if (message != null && message!.isNotEmpty) ...[
            SizedBox(height: compact ? DesignTokens.spacingXs : DesignTokens.spacingMd),
            Text(
              message!,
              style: TextStyleHelper.bodyMedium(context),
              textAlign: TextAlign.center,
            ),
          ],
          
          // Action button
          if (actionButtonText != null && actionButtonText!.isNotEmpty) ...[
            SizedBox(height: compact ? DesignTokens.spacingMd : DesignTokens.spacingLg),
            AppButton(
              text: actionButtonText!,
              onPressed: onActionButtonPressed,
              type: AppButtonType.primary,
              size: compact ? AppButtonSize.small : AppButtonSize.medium,
            ),
          ],
          
          // Secondary action button
          if (secondaryActionButtonText != null && secondaryActionButtonText!.isNotEmpty) ...[
            SizedBox(height: DesignTokens.spacingMd),
            AppButton(
              text: secondaryActionButtonText!,
              onPressed: onSecondaryActionButtonPressed,
              type: AppButtonType.outline,
              size: compact ? AppButtonSize.small : AppButtonSize.medium,
            ),
          ],
        ],
      ),
    );
  }
}
