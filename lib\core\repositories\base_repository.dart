
// Interface for repositories that support sync
abstract class SyncableRepository<T> {
  // Add a record and mark for sync
  Future<int> add(T entity);
  
  // Update a record and mark for sync
  Future<bool> update(T entity);
  
  // Delete a record (soft delete) and mark for sync
  Future<bool> delete(int id);
  
  // Get a single record by ID
  Future<T?> getById(int id);
  
  // Get all records
  Future<List<T>> getAll();
  
  // Trigger sync of this data type
  Future<void> syncData();
} 