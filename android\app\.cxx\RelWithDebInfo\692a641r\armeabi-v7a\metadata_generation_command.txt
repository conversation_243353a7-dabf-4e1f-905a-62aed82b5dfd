                        -HC:\Users\<USER>\AppData\Local\Programs\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\26.3.11579264
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\26.3.11579264\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=S:\DEV\englr\kiq\build\app\intermediates\cxx\RelWithDebInfo\692a641r\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=S:\DEV\englr\kiq\build\app\intermediates\cxx\RelWithDebInfo\692a641r\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BS:\DEV\englr\kiq\android\app\.cxx\RelWithDebInfo\692a641r\armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2